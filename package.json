{"name": "mhi-futures-tracker", "version": "1.0.0", "description": "Real-time Mini HSI Futures Price Tracker using FUTU API", "main": "app.js", "bin": "app.js", "scripts": {"start": "node server.js", "select": "node contract-selector.js", "dev": "nodemon server.js", "console": "node start.js", "tracker": "node index.js", "test-connection": "node test-connection.js", "test-login": "node test-futu-login.js", "diagnose": "node futu-visual-diagnostic.js", "quick-test": "node futu-visual-diagnostic.js --quick", "build": "pkg . --out-path dist", "build-win": "pkg . --targets node18-win-x64 --out-path dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["futu", "hsi", "futures", "real-time", "trading"], "author": "", "license": "ISC", "dependencies": {"bytebuffer": "^5.0.1", "express": "^4.18.2", "long": "^4.0.0", "protobufjs": "^6.8.8", "socket.io": "^4.7.2", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.1", "pkg": "^5.8.1"}, "pkg": {"scripts": ["app.js"], "assets": ["public/**/*", "proto/**/*"], "targets": ["node18-win-x64"], "outputPath": "dist"}}