# 恆生指數期貨實時追蹤器 - 網頁版

## 🌐 網頁版特色

這是一個現代化的網頁版恆生指數期貨實時價格追蹤器，提供直觀的用戶界面和豐富的功能。

### ✨ 主要功能

- **🔄 實時價格更新**: 每秒更新的期貨價格和成交量
- **📊 互動式圖表**: 使用 Chart.js 的實時價格走勢圖
- **📱 響應式設計**: 支持桌面和移動設備
- **🎨 現代化界面**: 美觀的漸變背景和卡片式設計
- **📈 統計信息**: 當日最高價、最低價、成交量等
- **📋 實時數據流**: 滾動顯示的實時交易數據
- **💾 數據導出**: 支持 CSV 格式數據導出
- **🔗 WebSocket 連接**: 穩定的實時數據傳輸

### 🎯 界面組件

#### 1. 狀態欄
- 連接狀態指示器
- 當前追蹤的期貨合約
- 使用的帳號信息

#### 2. 價格顯示卡片
- 大字體顯示當前價格
- 價格變化和百分比
- 最後更新時間

#### 3. 統計信息網格
- 成交量
- 當日最高價
- 當日最低價
- 更新頻率

#### 4. 實時圖表
- 價格走勢線圖
- 可清除圖表數據
- 自動縮放和滾動

#### 5. 數據流面板
- 實時交易記錄
- 自動滾動功能
- 顏色編碼（綠色上漲，紅色下跌）

#### 6. 控制面板
- 重新連接按鈕
- 數據導出功能

## 🚀 啟動方法

### 1. 安裝依賴
```bash
npm install
```

### 2. 啟動網頁服務器
```bash
npm start
```

### 3. 訪問網頁
在瀏覽器中打開：http://localhost:3000

### 4. 開發模式（可選）
```bash
npm run dev  # 使用 nodemon 自動重啟
```

## 🔧 配置說明

### 服務器配置
- **端口**: 3000（可通過環境變量 PORT 修改）
- **靜態文件**: public 目錄
- **WebSocket**: Socket.IO 實時通信

### FUTU API 配置
```javascript
const CONFIG = {
    host: '127.0.0.1',           // FUTU OpenD 地址
    port: 33333,                 // WebSocket 端口
    enableSSL: false,            // SSL 設置
    websocketKey: '3e8229abd3ccdfdc',  // WebSocket 密鑰
    futuAccount: '********'      // 牛牛號
};
```

### 期貨合約設置
```javascript
const HSI_FUTURES = {
    market: 1,        // 香港市場
    code: 'MHI2501'   // Mini HSI 期貨代碼
};
```

## 📱 使用指南

### 基本操作
1. **查看實時價格**: 主要價格卡片顯示當前價格和變化
2. **監控走勢**: 圖表區域顯示價格走勢線
3. **查看統計**: 統計卡片顯示關鍵指標
4. **追蹤數據流**: 底部面板顯示實時交易記錄

### 進階功能
1. **重新連接**: 點擊"重新連接"按鈕重新建立連接
2. **清除圖表**: 清空圖表數據重新開始
3. **清除數據流**: 清空實時數據記錄
4. **控制滾動**: 開啟/關閉數據流自動滾動
5. **導出數據**: 下載 CSV 格式的歷史數據

### 狀態指示
- **🟢 已連接**: 綠色圓點表示正常連接
- **🔴 未連接**: 紅色圓點表示連接中斷
- **📈 價格上漲**: 綠色顯示和向上箭頭
- **📉 價格下跌**: 紅色顯示和向下箭頭

## 🛠 技術架構

### 前端技術
- **HTML5**: 語義化標記
- **CSS3**: 現代樣式和動畫
- **JavaScript ES6+**: 現代 JavaScript 特性
- **Chart.js**: 圖表庫
- **Socket.IO Client**: 實時通信
- **Font Awesome**: 圖標庫

### 後端技術
- **Node.js**: 服務器運行環境
- **Express.js**: Web 框架
- **Socket.IO**: 實時通信服務器
- **WebSocket**: FUTU API 連接

### 數據流
```
FUTU OpenD → WebSocket → Node.js Server → Socket.IO → Web Browser
```

## 📊 數據格式

### 價格更新數據
```javascript
{
    timestamp: "2025-01-26T14:30:25.123Z",
    price: 19850.50,
    volume: 12500,
    change: 5.25,
    changePercent: 0.026
}
```

### 狀態數據
```javascript
{
    connected: true,
    contract: "MHI2501",
    account: "********",
    timestamp: "2025-01-26T14:30:25.123Z"
}
```

## 🔍 故障排除

### 常見問題

#### 1. 無法連接到服務器
- 確認 Node.js 服務器正在運行
- 檢查端口 3000 是否被占用
- 確認防火牆設置

#### 2. 沒有實時數據
- 檢查 FUTU OpenD 是否運行
- 驗證 WebSocket 配置
- 查看瀏覽器控制台錯誤

#### 3. 圖表不顯示
- 確認 Chart.js 庫已載入
- 檢查瀏覽器兼容性
- 查看 JavaScript 錯誤

#### 4. 數據導出失敗
- 確認瀏覽器支持下載功能
- 檢查是否有數據可導出
- 驗證文件權限

### 調試方法
1. 打開瀏覽器開發者工具
2. 查看 Console 標籤的錯誤信息
3. 檢查 Network 標籤的網絡請求
4. 查看服務器控制台輸出

## 🔮 未來功能

### 計劃中的功能
- **多合約支持**: 同時追蹤多個期貨合約
- **技術指標**: 添加移動平均線、RSI 等指標
- **價格警報**: 設置價格提醒功能
- **歷史數據**: 查看歷史價格數據
- **用戶設置**: 個性化界面設置
- **深色模式**: 支持深色主題

### 技術改進
- **PWA 支持**: 漸進式網頁應用
- **離線功能**: 離線數據查看
- **推送通知**: 瀏覽器推送提醒
- **多語言**: 支持英文界面

## 📄 授權

此項目僅供學習和研究使用。請遵守相關法規和交易所規則。
