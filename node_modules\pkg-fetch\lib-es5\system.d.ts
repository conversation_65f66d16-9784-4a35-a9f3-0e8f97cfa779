export declare function abiToNodeRange(abi: string): string;
export declare function isValidNodeRange(nodeRange: string): boolean;
export declare function toFancyPlatform(platform: string): string;
export declare function toFancyArch(arch: string): string;
export declare const hostAbi: string;
export declare const hostPlatform: string;
export declare const knownPlatforms: string[];
export declare const hostArch: string;
export declare const targetArchs: string[];
export declare const knownArchs: string[];
//# sourceMappingURL=system.d.ts.map