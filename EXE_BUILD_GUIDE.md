# 恆生指數期貨追蹤器 - 獨立 EXE 程式

## 🎉 成功創建獨立可執行程式！

我已經成功將網頁版程式打包成獨立的 Windows 可執行文件 (.exe)，可以脫離 VS Code 和 Node.js 環境運行。

## 📁 打包結果

### 文件位置
```
dist/
├── mhi-futures-tracker.exe    # 主程序文件 (約 50MB)
├── 啟動程序.bat               # 快速啟動腳本
├── 使用說明.txt               # 詳細使用說明
└── public/                    # 網頁界面文件
    ├── index.html
    ├── style.css
    └── app.js
```

### 文件大小
- **mhi-futures-tracker.exe**: ~50MB (包含 Node.js 運行時)
- **總大小**: ~52MB

## 🚀 使用方法

### 方法1: 快速啟動 (推薦)
1. 雙擊 `啟動程序.bat`
2. 程序會自動啟動並打開瀏覽器

### 方法2: 直接運行
1. 雙擊 `mhi-futures-tracker.exe`
2. 手動打開瀏覽器訪問顯示的網址

## ✨ 獨立程式特色

### 🔧 技術特點
- ✅ **完全獨立**: 無需安裝 Node.js 或其他依賴
- ✅ **自包含**: 包含所有必要的運行時和庫
- ✅ **跨機器**: 可在任何 Windows 電腦上運行
- ✅ **便攜式**: 可複製到 USB 或其他電腦使用

### 🌐 功能特色
- ✅ **自動端口選擇**: 智能選擇可用端口 (3000-3003, 8080-8081)
- ✅ **自動打開瀏覽器**: 啟動後自動打開網頁界面
- ✅ **智能數據模式**: 自動切換真實數據/模擬數據
- ✅ **現代化界面**: 完整的網頁版功能
- ✅ **實時更新**: 1秒級數據更新

### 📊 數據模式
- **真實數據模式**: 當 FUTU OpenD 運行時自動啟用
- **模擬數據模式**: 當無法連接時提供演示數據

## 🔧 配置信息

程式內建配置：
```javascript
帳號: 22188140
密碼: Leo@0609
WebSocket 密鑰: 3e8229abd3ccdfdc
目標合約: MHI2501 (Mini HSI)
```

## 📱 使用流程

1. **啟動程式**
   - 雙擊啟動文件
   - 程式會顯示啟動信息

2. **自動打開瀏覽器**
   - 程式會自動打開預設瀏覽器
   - 顯示現代化的網頁界面

3. **查看數據**
   - 如果 FUTU OpenD 運行：顯示真實數據
   - 否則：顯示模擬數據供演示

4. **使用功能**
   - 查看實時價格和圖表
   - 導出數據
   - 控制圖表和數據流

## 🔗 FUTU OpenD 連接

要獲取真實數據，請確保：

### 必要設置
1. **FUTU OpenD 已啟動**
   - 使用帳號 22188140 登錄
   - WebSocket 服務已啟用

2. **網絡配置**
   - 端口 33333 未被阻擋
   - 防火牆允許連接

3. **API 權限**
   - 帳號有 OpenAPI 權限
   - 有期貨 LV1 權限

## 🛠 打包技術

### 使用的工具
- **pkg**: Node.js 應用打包工具
- **目標平台**: Windows x64
- **Node.js 版本**: 18.x

### 打包命令
```bash
# 安裝打包工具
npm install pkg --save-dev

# 執行打包
npm run build-win

# 或手動打包
npx pkg app.js --targets node18-win-x64 --out-path dist
```

## 📋 系統要求

### 最低要求
- **操作系統**: Windows 7/8/10/11 (64位)
- **內存**: 512MB 可用內存
- **磁盤空間**: 100MB 可用空間
- **網絡**: 互聯網連接 (用於瀏覽器界面)

### 推薦配置
- **操作系統**: Windows 10/11
- **內存**: 2GB 或更多
- **瀏覽器**: Chrome, Firefox, Edge (最新版本)

## 🔍 故障排除

### 常見問題

#### 1. 程式無法啟動
- 檢查是否有防毒軟件阻擋
- 嘗試以管理員身份運行
- 確認 Windows 版本兼容性

#### 2. 瀏覽器未自動打開
- 手動打開瀏覽器
- 訪問控制台顯示的網址
- 檢查預設瀏覽器設置

#### 3. 無法連接 FUTU OpenD
- 確認 FUTU OpenD 已啟動
- 檢查防火牆設置
- 驗證帳號和密碼

#### 4. 端口被占用
- 程式會自動嘗試其他端口
- 關閉占用端口的其他程式
- 重新啟動程式

## 📦 分發說明

### 分發文件
要分發給其他用戶，請提供整個 `dist` 文件夾：
```
dist/
├── mhi-futures-tracker.exe
├── 啟動程序.bat
├── 使用說明.txt
└── public/
```

### 安裝說明
1. 將整個 `dist` 文件夾複製到目標電腦
2. 雙擊 `啟動程序.bat` 即可使用
3. 無需安裝任何額外軟件

## 🎯 優勢總結

### 對比原版本
| 特性 | 原版本 | 獨立 EXE 版 |
|------|--------|-------------|
| 運行環境 | 需要 VS Code + Node.js | 完全獨立 |
| 安裝複雜度 | 複雜 | 零安裝 |
| 便攜性 | 低 | 高 |
| 分發難度 | 高 | 低 |
| 用戶友好度 | 中 | 高 |

### 主要優勢
1. **零依賴**: 無需安裝 Node.js 或其他工具
2. **即用即開**: 雙擊即可運行
3. **便攜性強**: 可在任何 Windows 電腦使用
4. **專業外觀**: 自動啟動和瀏覽器集成
5. **功能完整**: 保留所有網頁版功能

## 🔮 未來改進

可能的改進方向：
- 添加桌面快捷方式創建
- 支持系統托盤運行
- 添加自動更新功能
- 創建安裝程序 (.msi)
- 支持多語言界面

---

**創建日期**: 2025-01-27  
**版本**: 1.0.0  
**文件大小**: ~52MB  
**支持平台**: Windows x64
