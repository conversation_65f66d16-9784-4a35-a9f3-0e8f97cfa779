// 簡單測試密碼更新
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',
    futuAccount: '********',
    password: 'Leo@0609'
};

console.log('🔐 密碼更新測試');
console.log('================');
console.log(`👤 帳號: ${CONFIG.futuAccount}`);
console.log(`🔑 密碼: ${CONFIG.password}`);
console.log(`🔐 WebSocket 密鑰: ${CONFIG.websocketKey}`);
console.log('================');
console.log('✅ 密碼已成功更新為: Leo@0609');
console.log('');
console.log('📝 更新的文件:');
console.log('- server.js');
console.log('- index.js');
console.log('- test-futu-login.js');
console.log('- README.md');
console.log('- LOGIN_SETUP.md');
console.log('- FINAL_STATUS.md');
console.log('');
console.log('🚀 請重新啟動服務器來應用新密碼:');
console.log('npm start');
