const FutuContractSearcher = require('./futu-contract-searcher');

// 測試 FUTU 合約搜尋
async function testFutuContracts() {
    console.log('🧪 測試 FUTU 小型恆生指數期貨合約搜尋');
    console.log('==========================================');
    
    const config = {
        host: '127.0.0.1',
        port: 33333,
        websocketKey: '3e8229abd3ccdfdc',
        futuAccount: '********',
        password: 'Leo@0609'
    };
    
    const searcher = new FutuContractSearcher(config);
    
    try {
        console.log('🔍 開始搜尋合約...');
        const contracts = await searcher.searchMHIContracts();
        
        console.log('');
        console.log('📊 搜尋結果:');
        console.log(`找到 ${contracts.length} 個合約`);
        console.log('');
        
        contracts.forEach((contract, index) => {
            console.log(`${index + 1}. ${contract.code} - ${contract.name}`);
            console.log(`   市場: ${contract.market}`);
            console.log(`   合約規模: ${contract.contractSize || 'N/A'}`);
            console.log(`   合約類型: ${contract.contractType || 'N/A'}`);
            console.log(`   到期日: ${contract.expiry || 'N/A'}`);
            console.log(`   成交量: ${contract.volume || 0}`);
            console.log(`   未平倉: ${contract.openInterest || 0}`);
            console.log(`   是否主連: ${contract.isMainContract ? '是' : '否'}`);
            console.log(`   是否推薦: ${contract.isRecommended ? '是' : '否'}`);
            console.log('');
        });
        
        // 測試推薦合約
        const recommended = searcher.getRecommendedContract(contracts);
        if (recommended) {
            console.log('💡 推薦合約:');
            console.log(`   ${recommended.code} - ${recommended.name}`);
            console.log('');
        }
        
        // 測試合約驗證
        console.log('🔍 測試合約驗證:');
        const testCodes = ['MHImain', 'MHI2501', 'INVALID'];
        
        for (const code of testCodes) {
            const isValid = contracts.find(c => c.code === code);
            console.log(`   ${code}: ${isValid ? '✅ 有效' : '❌ 無效'}`);
        }
        
        console.log('');
        console.log('✅ 測試完成！');
        
    } catch (error) {
        console.error('❌ 測試失敗:', error.message);
    }
}

// 測試預設合約生成
function testDefaultContracts() {
    console.log('🧪 測試預設合約生成');
    console.log('====================');
    
    const searcher = new FutuContractSearcher({});
    const contracts = searcher.getDefaultContracts();
    
    console.log(`生成了 ${contracts.length} 個預設合約:`);
    console.log('');
    
    contracts.slice(0, 5).forEach((contract, index) => {
        console.log(`${index + 1}. ${contract.code} - ${contract.name}`);
        console.log(`   到期日: ${contract.expiry || 'N/A'}`);
        console.log(`   成交量: ${contract.volume || 0}`);
        console.log(`   是否主連: ${contract.isMainContract ? '是' : '否'}`);
        console.log('');
    });
    
    console.log('✅ 預設合約生成測試完成！');
}

// 主測試函數
async function runTests() {
    console.log('🚀 開始 FUTU 合約測試');
    console.log('');
    
    // 測試預設合約
    testDefaultContracts();
    console.log('');
    
    // 測試真實 FUTU 連接
    await testFutuContracts();
}

// 如果直接運行此文件
if (require.main === module) {
    runTests().catch(error => {
        console.error('❌ 測試過程中發生錯誤:', error);
        process.exit(1);
    });
}

module.exports = { testFutuContracts, testDefaultContracts };
