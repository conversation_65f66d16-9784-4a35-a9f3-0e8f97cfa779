# 小型恆生指數期貨合約選擇指南

## 🎯 新功能：合約選擇

現在您可以選擇不同的小型恆生指數期貨合約進行追蹤，不再限制於固定的 MHI2501！

## 📊 可選合約列表

### 小型恆生指數期貨 (MHI) 合約
| 選項 | 合約代碼 | 到期月份 | 建議用途 |
|------|----------|----------|----------|
| 1 | MHI2501 | 2025年1月 | **推薦** - 最近月份，流動性最高 |
| 2 | MHI2502 | 2025年2月 | 次近月份，適合短期交易 |
| 3 | MHI2503 | 2025年3月 | 季月合約，適合中期持倉 |
| 4 | MHI2504 | 2025年4月 | 遠月合約，價格較穩定 |
| 5 | MHI2505 | 2025年5月 | 遠月合約，適合長期策略 |
| 6 | MHI2506 | 2025年6月 | 季月合約，適合季度策略 |

## 🚀 使用方法

### 方法1: 獨立 EXE 版本
1. 雙擊 `dist/啟動程序.bat`
2. 選擇合約 (1-6)
3. 程式會自動啟動並追蹤選定的合約

```
📊 請選擇小型恆生指數期貨合約:

1. MHI2501 - 2025年1月到期
2. MHI2502 - 2025年2月到期  
3. MHI2503 - 2025年3月到期
4. MHI2504 - 2025年4月到期
5. MHI2505 - 2025年5月到期
6. MHI2506 - 2025年6月到期

請輸入選項 (1-6，預設為1): 
```

### 方法2: 開發環境 - 互動式選擇器
```bash
npm run select
```
提供完整的互動式選單，包括：
- 合約選擇
- 啟動選項 (網頁版/控制台版)
- 連接診斷
- 重新選擇

### 方法3: 開發環境 - 命令行參數
```bash
# 直接指定合約啟動
node app.js --contract=MHI2503

# 或使用 npm 腳本
npm start -- --contract=MHI2502
```

### 方法4: 開發環境 - 預設啟動
```bash
npm start  # 使用預設合約 MHI2501
```

## 💡 合約選擇建議

### 🏆 最佳選擇：MHI2501 (選項1)
- **流動性最高**: 買賣價差最小
- **成交量最大**: 容易進出場
- **價格發現**: 最能反映市場真實價格
- **適合**: 日內交易、短期策略

### 📈 其他選擇考量

#### 近月合約 (MHI2501, MHI2502)
- ✅ 流動性好
- ✅ 價差小
- ✅ 適合短期交易
- ⚠️ 接近到期時需要轉倉

#### 遠月合約 (MHI2504, MHI2505)
- ✅ 價格較穩定
- ✅ 適合長期持倉
- ⚠️ 流動性較低
- ⚠️ 價差較大

#### 季月合約 (MHI2503, MHI2506)
- ✅ 適合季度策略
- ✅ 到期時間較規律
- ⚠️ 流動性中等

## 🔧 技術實現

### 前端顯示
- 網頁界面會顯示完整的合約信息
- 格式：`MHI2503 (2025年3月到期)`
- 包含工具提示說明

### 後端處理
- 自動驗證合約代碼有效性
- 無效合約自動回退到 MHI2501
- 支援命令行參數傳遞

### 數據處理
- 所有功能保持一致
- 實時數據、圖表、統計都會更新為選定合約
- 模擬數據也會使用選定合約名稱

## 📋 使用場景

### 日內交易者
**推薦**: MHI2501
- 最高流動性
- 最小價差
- 最佳執行價格

### 短期投資者 (1-2週)
**推薦**: MHI2501 或 MHI2502
- 良好流動性
- 合理價差
- 靈活進出

### 中期投資者 (1-3個月)
**推薦**: MHI2503 或 MHI2504
- 避免頻繁轉倉
- 價格相對穩定
- 適合趨勢跟蹤

### 長期投資者 (3個月以上)
**推薦**: MHI2505 或 MHI2506
- 長期持倉
- 減少轉倉成本
- 適合基本面分析

## ⚠️ 注意事項

### 合約到期
- 注意各合約的到期日
- 提前規劃轉倉時間
- 避免實物交割

### 流動性風險
- 遠月合約流動性較低
- 大額交易可能影響價格
- 建議分批進出場

### 價差考量
- 近月合約價差較小
- 遠月合約價差較大
- 影響交易成本

## 🔄 切換合約

### 運行中切換
目前需要重新啟動程式來切換合約

### 未來改進
計劃添加：
- 網頁界面動態切換
- 多合約同時監控
- 合約價差分析

## 📊 顯示效果

### 控制台輸出
```
🚀 啟動獨立版恆生指數期貨追蹤器...
📊 目標合約: MHI2503 (2025年3月到期)
🔑 使用帳號: 22188140
```

### 網頁界面
- 狀態欄顯示：`MHI2503 (2025年3月到期)`
- 工具提示：`小型恆生指數期貨 - 2025年3月到期`
- 圖表標題會更新為選定合約

## 🎯 總結

新的合約選擇功能讓您可以：

1. **靈活選擇**: 6個不同到期月份的合約
2. **簡單操作**: 啟動時選擇，無需修改代碼
3. **完整功能**: 所有功能都支援選定的合約
4. **智能處理**: 自動驗證和錯誤處理

這大大提升了程式的實用性和靈活性，讓您可以根據自己的交易策略選擇最適合的期貨合約進行追蹤！
