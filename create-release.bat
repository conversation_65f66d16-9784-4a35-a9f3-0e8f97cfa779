@echo off
echo 📦 正在創建發布包...
echo ========================

set RELEASE_NAME=恆生指數期貨追蹤器_v1.0.0
set RELEASE_DIR=release

echo 📁 創建發布目錄...
if exist "%RELEASE_DIR%" rmdir /s /q "%RELEASE_DIR%"
mkdir "%RELEASE_DIR%"
mkdir "%RELEASE_DIR%\%RELEASE_NAME%"

echo 📋 複製程序文件...
copy "dist\mhi-futures-tracker.exe" "%RELEASE_DIR%\%RELEASE_NAME%\"
copy "dist\啟動程序.bat" "%RELEASE_DIR%\%RELEASE_NAME%\"
copy "dist\使用說明.txt" "%RELEASE_DIR%\%RELEASE_NAME%\"
xcopy "dist\public" "%RELEASE_DIR%\%RELEASE_NAME%\public\" /E /Y /Q

echo 📄 複製文檔...
copy "EXE_BUILD_GUIDE.md" "%RELEASE_DIR%\%RELEASE_NAME%\技術文檔.md"
copy "README.md" "%RELEASE_DIR%\%RELEASE_NAME%\項目說明.md"

echo 📝 創建快速開始指南...
echo 恆生指數期貨實時追蹤器 - 快速開始 > "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo ================================== >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo. >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 🚀 啟動方法: >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 1. 雙擊 "啟動程序.bat" >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 2. 等待瀏覽器自動打開 >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 3. 開始使用！ >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo. >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 📊 功能特色: >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo ✅ 實時期貨價格追蹤 >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo ✅ 現代化網頁界面 >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo ✅ 智能數據模式切換 >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo ✅ 完全獨立運行 >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo. >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"
echo 💡 詳細說明請查看 "使用說明.txt" >> "%RELEASE_DIR%\%RELEASE_NAME%\快速開始.txt"

echo ✅ 發布包創建完成！
echo 📍 位置: %RELEASE_DIR%\%RELEASE_NAME%\
echo 📦 可以將此文件夾打包分發給用戶

pause
