const FutuVisualConnector = require('./futu-visual-connector');

// 測試 FUTU 期貨合約訂閱
class FutuSubscriptionTest {
    constructor() {
        this.config = {
            host: '127.0.0.1',
            port: 33333,
            websocketKey: '3e8229abd3ccdfdc',
            futuAccount: '********',
            password: '<PERSON>@0609'
        };
        
        this.connector = null;
        this.testContracts = ['MHImain', 'HSImain', 'MHI2507'];
    }

    async runSubscriptionTest() {
        console.log('🧪 測試 FUTU 期貨合約訂閱功能');
        console.log('=====================================');
        
        try {
            // 創建連接器
            this.connector = new FutuVisualConnector(this.config);
            
            // 設置事件監聽
            this.setupEventHandlers();
            
            // 連接到 FUTU OpenD
            console.log('🔗 正在連接到 FUTU OpenD...');
            const connected = await this.connector.connect();
            
            if (connected) {
                console.log('✅ 連接成功！');
                
                // 等待連接穩定
                await this.delay(2000);
                
                // 測試訂閱不同的合約
                await this.testContractSubscriptions();
                
                // 等待數據
                console.log('⏳ 等待實時數據...');
                await this.delay(30000); // 等待30秒
                
            } else {
                console.log('❌ 連接失敗');
            }
            
        } catch (error) {
            console.error('❌ 測試失敗:', error.message);
        } finally {
            if (this.connector) {
                this.connector.disconnect();
            }
        }
    }

    setupEventHandlers() {
        this.connector.onConnect = () => {
            console.log('🎉 WebSocket 連接建立');
        };

        this.connector.onDisconnect = () => {
            console.log('👋 WebSocket 連接斷開');
        };

        this.connector.onError = (error) => {
            console.log('❌ WebSocket 錯誤:', error.message);
        };

        this.connector.onData = (data) => {
            this.handleDataResponse(data);
        };
    }

    handleDataResponse(data) {
        try {
            const response = JSON.parse(data.toString());
            const protocol = response.Protocol || response.cmd || 'Unknown';
            
            console.log(`📨 收到響應: ${protocol}`);
            
            switch (protocol) {
                case 'Qot_Sub':
                    this.handleSubscriptionResponse(response);
                    break;
                    
                case 'Qot_UpdateBasicQot':
                    this.handleBasicQuoteUpdate(response);
                    break;
                    
                case 'Qot_UpdateTicker':
                    this.handleTickerUpdate(response);
                    break;
                    
                default:
                    console.log('📄 其他響應:', JSON.stringify(response, null, 2));
                    break;
            }
            
        } catch (error) {
            console.log('⚠️  無法解析響應:', error.message);
            console.log('📄 原始數據:', data.toString());
        }
    }

    handleSubscriptionResponse(response) {
        console.log('📡 訂閱響應處理');
        
        if (response.RetData && response.RetData.s2c) {
            const s2c = response.RetData.s2c;
            
            if (s2c.securitySubList) {
                s2c.securitySubList.forEach(sub => {
                    const security = sub.security;
                    const subTypes = sub.subTypeList || [];
                    
                    console.log(`✅ 訂閱確認: ${security.code} (市場: ${security.market})`);
                    console.log(`   訂閱類型: ${subTypes.join(', ')}`);
                });
            }
        }
    }

    handleBasicQuoteUpdate(response) {
        console.log('📊 基本報價更新');
        
        if (response.RetData && response.RetData.s2c && response.RetData.s2c.basicQotList) {
            const basicQotList = response.RetData.s2c.basicQotList;
            
            basicQotList.forEach(quote => {
                const security = quote.security;
                const basicQot = quote.basicQot;
                
                console.log(`📈 ${security.code} 報價:`);
                console.log(`   最新價: ${basicQot.curPrice}`);
                console.log(`   開盤價: ${basicQot.openPrice}`);
                console.log(`   最高價: ${basicQot.highPrice}`);
                console.log(`   最低價: ${basicQot.lowPrice}`);
                console.log(`   成交量: ${basicQot.volume}`);
                console.log(`   更新時間: ${new Date().toLocaleTimeString()}`);
                console.log('');
            });
        }
    }

    handleTickerUpdate(response) {
        console.log('📈 逐筆數據更新');
        
        if (response.RetData && response.RetData.s2c && response.RetData.s2c.tickerList) {
            const tickerList = response.RetData.s2c.tickerList;
            
            tickerList.forEach(ticker => {
                const security = ticker.security;
                console.log(`🔄 ${security.code} 逐筆數據:`);
                
                if (ticker.tickerList) {
                    ticker.tickerList.forEach(tick => {
                        console.log(`   ${tick.time}: 價格 ${tick.price}, 成交量 ${tick.volume}`);
                    });
                }
            });
        }
    }

    async testContractSubscriptions() {
        console.log('🔍 測試合約訂閱...');
        
        for (const contract of this.testContracts) {
            console.log(`📡 測試訂閱合約: ${contract}`);
            
            const success = await this.connector.subscribeToContract(contract);
            
            if (success) {
                console.log(`✅ 訂閱請求已發送: ${contract}`);
            } else {
                console.log(`❌ 訂閱請求失敗: ${contract}`);
            }
            
            // 等待響應
            await this.delay(3000);
        }
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主測試函數
async function runTest() {
    const test = new FutuSubscriptionTest();
    await test.runSubscriptionTest();
}

// 如果直接運行此文件
if (require.main === module) {
    runTest().catch(error => {
        console.error('❌ 測試過程中發生錯誤:', error);
        process.exit(1);
    });
}

module.exports = FutuSubscriptionTest;
