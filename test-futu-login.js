const FutuAPI = require('./futu-api');

// 配置信息
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',
    futuAccount: '********',
    password: 'Leo@0609'
};

async function testFutuLogin() {
    console.log('🧪 測試 FUTU API 登錄連接...');
    console.log(`📍 連接地址: ws://${CONFIG.host}:${CONFIG.port}`);
    console.log(`👤 帳號: ${CONFIG.futuAccount}`);
    console.log(`🔑 密碼: ${CONFIG.password.replace(/./g, '*')}`);
    console.log(`🔐 WebSocket 密鑰: ${CONFIG.websocketKey}`);
    
    const futuAPI = new FutuAPI();
    
    // 設置事件監聽器
    futuAPI.onConnect = () => {
        console.log('✅ FUTU API 連接成功！');
        console.log('🎉 登錄驗證通過');
        
        // 測試獲取用戶信息
        testUserInfo(futuAPI);
    };
    
    futuAPI.onDisconnect = () => {
        console.log('❌ FUTU API 連接斷開');
    };
    
    futuAPI.onError = (error) => {
        console.error('🚨 FUTU API 錯誤:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n💡 故障排除建議:');
            console.log('1. 確認 FUTU OpenD 已啟動');
            console.log('2. 檢查 WebSocket 端口是否為 33333');
            console.log('3. 確認防火牆設置');
        } else if (error.message.includes('login') || error.message.includes('auth')) {
            console.log('\n💡 登錄問題建議:');
            console.log('1. 檢查帳號和密碼是否正確');
            console.log('2. 確認帳號有 API 使用權限');
            console.log('3. 檢查 WebSocket 密鑰是否正確');
        }
    };
    
    try {
        // 嘗試連接
        await futuAPI.connect(
            CONFIG.host,
            CONFIG.port,
            CONFIG.enableSSL,
            CONFIG.websocketKey,
            CONFIG.futuAccount,
            CONFIG.password
        );
        
    } catch (error) {
        console.error('❌ 連接失敗:', error.message);
        process.exit(1);
    }
}

async function testUserInfo(futuAPI) {
    try {
        console.log('\n📋 正在獲取用戶信息...');
        
        // 這裡可以添加獲取用戶信息的 API 調用
        // const userInfo = await futuAPI.getUserInfo();
        // console.log('👤 用戶信息:', userInfo);
        
        console.log('✅ 基本連接測試完成');
        
        // 測試期貨數據訂閱
        await testFuturesSubscription(futuAPI);
        
    } catch (error) {
        console.error('❌ 獲取用戶信息失敗:', error.message);
    }
}

async function testFuturesSubscription(futuAPI) {
    try {
        console.log('\n📈 正在測試期貨數據訂閱...');
        
        const HSI_FUTURES = {
            market: 1,  // 香港市場
            code: 'MHI2501'  // Mini HSI 期貨代碼
        };
        
        // 訂閱基礎行情
        const subRequest = {
            c2s: {
                securityList: [HSI_FUTURES],
                subTypeList: [1], // 基礎報價
                isSubOrUnSub: true,
                isRegOrUnRegPush: true
            }
        };

        console.log(`📊 正在訂閱 ${HSI_FUTURES.code} 期貨數據...`);
        
        const response = await futuAPI.subscribe(subRequest);
        console.log('✅ 訂閱成功:', response);
        
        // 設置數據推送監聽
        futuAPI.onQuotePush = (data) => {
            console.log('📨 收到實時數據:', data);
        };
        
        console.log('🎯 測試完成！等待實時數據推送...');
        console.log('按 Ctrl+C 退出測試');
        
    } catch (error) {
        console.error('❌ 訂閱失敗:', error.message);
    }
}

// 處理程序退出
process.on('SIGINT', () => {
    console.log('\n🛑 正在關閉測試程序...');
    process.exit(0);
});

// 啟動測試
testFutuLogin().catch(error => {
    console.error('❌ 測試失敗:', error);
    process.exit(1);
});
