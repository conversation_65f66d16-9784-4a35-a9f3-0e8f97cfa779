// 全局變量
let socket;
let priceChart;
let priceData = [];
let autoScroll = true;
let dayHigh = null;
let dayLow = null;
let availableContracts = [];
let currentContract = null;

// 初始化應用程序
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    initializeChart();
    initializeContractSelector();
    hideLoadingOverlay();
});

// 初始化 Socket.IO 連接
function initializeSocket() {
    socket = io();
    
    // 連接事件
    socket.on('connect', function() {
        console.log('✅ 已連接到服務器');
        updateConnectionStatus(true);
    });
    
    socket.on('disconnect', function() {
        console.log('❌ 與服務器斷開連接');
        updateConnectionStatus(false);
    });
    
    // 狀態更新
    socket.on('status', function(data) {
        updateStatus(data);
    });
    
    // 價格更新
    socket.on('price_update', function(data) {
        updatePrice(data);
        addToDataStream(data);
        updateChart(data);
        updateStatistics(data);
    });
    
    // 歷史數據
    socket.on('history', function(data) {
        console.log('📊 收到歷史數據:', data.length, '筆記錄');
        priceData = data;
        if (data.length > 0) {
            initializeStatistics(data);
            updateChartWithHistory(data);
        }
    });

    // 可用合約列表
    socket.on('contracts_list', function(data) {
        console.log('📋 收到可用合約列表:', data.length, '個合約');
        availableContracts = data;
        updateContractList();
    });

    // 合約切換成功
    socket.on('contract_changed', function(data) {
        console.log('✅ 合約已切換:', data.contract.code);
        currentContract = data.contract;
        updateCurrentContract();
        closeContractModal();

        // 清除圖表和統計
        clearChart();
        dayHigh = null;
        dayLow = null;
        document.getElementById('day-high').textContent = '--';
        document.getElementById('day-low').textContent = '--';
    });

    // 合約切換錯誤
    socket.on('contract_change_error', function(data) {
        console.error('❌ 合約切換失敗:', data.error);
        alert(`合約切換失敗: ${data.error}`);
    });
}

// 初始化圖表
function initializeChart() {
    const ctx = document.getElementById('price-chart').getContext('2d');
    
    priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '價格',
                data: [],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 0,
                pointHoverRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#3498db',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// 更新連接狀態
function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connection-status');
    if (connected) {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 已連接';
        statusElement.className = 'status-value connected';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 未連接';
        statusElement.className = 'status-value disconnected';
    }
}

// 更新狀態信息
function updateStatus(data) {
    // 更新合約信息
    const contractElement = document.getElementById('contract-code');
    if (data.contractName) {
        contractElement.textContent = `${data.contract} (${data.contractName})`;
        contractElement.title = `小型恆生指數期貨 - ${data.contractName}`;
    } else {
        contractElement.textContent = data.contract;
    }

    document.getElementById('account-id').textContent = data.account;
    updateConnectionStatus(data.connected);

    // 更新數據類型顯示
    if (data.dataType) {
        const dataTypeElement = document.getElementById('data-type');
        if (dataTypeElement) {
            if (data.dataType === 'real') {
                dataTypeElement.innerHTML = '<i class="fas fa-database"></i> 真實數據';
                dataTypeElement.className = 'status-value connected';
            } else {
                dataTypeElement.innerHTML = '<i class="fas fa-flask"></i> 模擬數據';
                dataTypeElement.className = 'status-value disconnected';
            }
        }
    }
}

// 更新價格顯示
function updatePrice(data) {
    const currentPriceElement = document.getElementById('current-price');
    const priceChangeElement = document.getElementById('price-change');
    const priceChangePercentElement = document.getElementById('price-change-percent');
    const lastUpdateElement = document.getElementById('last-update');
    
    // 更新當前價格
    currentPriceElement.textContent = data.price.toFixed(2);
    
    // 更新價格變化
    const change = data.change;
    const changePercent = data.changePercent;
    
    let changeClass = 'change-neutral';
    let changeIcon = 'fas fa-minus';
    
    if (change > 0) {
        changeClass = 'change-positive';
        changeIcon = 'fas fa-arrow-up';
    } else if (change < 0) {
        changeClass = 'change-negative';
        changeIcon = 'fas fa-arrow-down';
    }
    
    priceChangeElement.className = changeClass;
    priceChangeElement.innerHTML = `<i class="${changeIcon}"></i> ${change.toFixed(2)}`;
    priceChangePercentElement.textContent = `(${changePercent.toFixed(2)}%)`;
    priceChangePercentElement.className = `change-percent ${changeClass}`;
    
    // 更新最後更新時間
    const updateTime = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    lastUpdateElement.textContent = updateTime;
}

// 添加到數據流
function addToDataStream(data) {
    const streamElement = document.getElementById('data-stream');
    const time = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    
    let changeClass = '';
    let changeIcon = '➡️';
    
    if (data.change > 0) {
        changeClass = 'positive';
        changeIcon = '📈';
    } else if (data.change < 0) {
        changeClass = 'negative';
        changeIcon = '📉';
    }
    
    const streamItem = document.createElement('div');
    streamItem.className = `stream-item ${changeClass}`;
    streamItem.innerHTML = `
        <strong>${time}</strong> | 
        💰 ${data.price.toFixed(2)} ${changeIcon} (${data.change.toFixed(2)}) | 
        📊 成交量: ${data.volume.toLocaleString()}
    `;
    
    streamElement.appendChild(streamItem);
    
    // 保持最多100條記錄
    while (streamElement.children.length > 100) {
        streamElement.removeChild(streamElement.firstChild);
    }
    
    // 自動滾動到底部
    if (autoScroll) {
        streamElement.scrollTop = streamElement.scrollHeight;
    }
}

// 更新圖表
function updateChart(data) {
    const time = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    
    priceChart.data.labels.push(time);
    priceChart.data.datasets[0].data.push(data.price);
    
    // 保持最多50個數據點
    if (priceChart.data.labels.length > 50) {
        priceChart.data.labels.shift();
        priceChart.data.datasets[0].data.shift();
    }
    
    priceChart.update('none');
}

// 使用歷史數據更新圖表
function updateChartWithHistory(historyData) {
    const labels = [];
    const prices = [];
    
    // 取最近50筆數據
    const recentData = historyData.slice(-50);
    
    recentData.forEach(item => {
        const time = new Date(item.timestamp).toLocaleTimeString('zh-TW');
        labels.push(time);
        prices.push(item.price);
    });
    
    priceChart.data.labels = labels;
    priceChart.data.datasets[0].data = prices;
    priceChart.update();
}

// 更新統計信息
function updateStatistics(data) {
    // 更新成交量
    document.getElementById('volume').textContent = data.volume.toLocaleString();
    
    // 更新最高價和最低價
    if (dayHigh === null || data.price > dayHigh) {
        dayHigh = data.price;
        document.getElementById('day-high').textContent = dayHigh.toFixed(2);
    }
    
    if (dayLow === null || data.price < dayLow) {
        dayLow = data.price;
        document.getElementById('day-low').textContent = dayLow.toFixed(2);
    }
}

// 初始化統計信息
function initializeStatistics(historyData) {
    if (historyData.length === 0) return;
    
    const prices = historyData.map(item => item.price);
    dayHigh = Math.max(...prices);
    dayLow = Math.min(...prices);
    
    document.getElementById('day-high').textContent = dayHigh.toFixed(2);
    document.getElementById('day-low').textContent = dayLow.toFixed(2);
    
    // 顯示最新的成交量
    const latestData = historyData[historyData.length - 1];
    document.getElementById('volume').textContent = latestData.volume.toLocaleString();
}

// 控制函數
function reconnect() {
    socket.emit('reconnect_request');
    showLoadingOverlay();
    setTimeout(hideLoadingOverlay, 3000);
}

function clearChart() {
    priceChart.data.labels = [];
    priceChart.data.datasets[0].data = [];
    priceChart.update();
}

function clearStream() {
    document.getElementById('data-stream').innerHTML = '';
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const scrollText = document.getElementById('scroll-text');
    scrollText.textContent = autoScroll ? '停止滾動' : '開始滾動';
}

function exportData() {
    if (priceData.length === 0) {
        alert('沒有數據可以導出');
        return;
    }
    
    const csvContent = "data:text/csv;charset=utf-8," 
        + "時間,價格,變化,變化百分比,成交量\n"
        + priceData.map(item => {
            const time = new Date(item.timestamp).toLocaleString('zh-TW');
            return `${time},${item.price},${item.change},${item.changePercent},${item.volume}`;
        }).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `HSI_futures_data_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showLoadingOverlay() {
    document.getElementById('loading-overlay').style.display = 'flex';
}

function hideLoadingOverlay() {
    document.getElementById('loading-overlay').style.display = 'none';
}

// 初始化合約選擇器
function initializeContractSelector() {
    const contractDisplay = document.getElementById('contract-code');
    const contractModal = document.getElementById('contract-modal');
    const closeModal = document.getElementById('close-modal');
    const cancelContract = document.getElementById('cancel-contract');
    const refreshContracts = document.getElementById('refresh-contracts');
    const contractSearch = document.getElementById('contract-search');

    // 點擊合約顯示區域打開彈窗
    contractDisplay.addEventListener('click', function() {
        openContractModal();
    });

    // 關閉彈窗
    closeModal.addEventListener('click', closeContractModal);
    cancelContract.addEventListener('click', closeContractModal);

    // 點擊彈窗外部關閉
    contractModal.addEventListener('click', function(e) {
        if (e.target === contractModal) {
            closeContractModal();
        }
    });

    // 重新搜尋合約
    refreshContracts.addEventListener('click', function() {
        refreshContractList();
    });

    // 搜尋功能
    contractSearch.addEventListener('input', function() {
        filterContracts(this.value);
    });

    // ESC 鍵關閉彈窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && contractModal.style.display === 'block') {
            closeContractModal();
        }
    });
}

// 打開合約選擇彈窗
function openContractModal() {
    const modal = document.getElementById('contract-modal');
    const arrow = document.getElementById('contract-arrow');

    modal.style.display = 'block';
    arrow.classList.add('rotated');

    // 請求最新的合約列表
    socket.emit('get_contracts');

    // 重置搜尋
    document.getElementById('contract-search').value = '';
}

// 關閉合約選擇彈窗
function closeContractModal() {
    const modal = document.getElementById('contract-modal');
    const arrow = document.getElementById('contract-arrow');

    modal.style.display = 'none';
    arrow.classList.remove('rotated');
}

// 更新合約列表
function updateContractList() {
    const contractList = document.getElementById('contract-list');

    if (availableContracts.length === 0) {
        contractList.innerHTML = `
            <div class="loading-contracts">
                <p>暫無可用合約</p>
            </div>
        `;
        return;
    }

    let html = '';

    availableContracts.forEach(contract => {
        const isCurrent = currentContract && contract.code === currentContract.code;
        const isRecommended = contract.isRecommended;
        const isActive = contract.volume > 0;

        let badges = '';
        if (isCurrent) badges += '<span class="badge current">當前</span>';
        if (isRecommended) badges += '<span class="badge recommended">推薦</span>';
        if (isActive) badges += '<span class="badge active">活躍</span>';

        let metaInfo = '';
        if (contract.volume > 0) {
            metaInfo += `成交量: ${contract.volume.toLocaleString()}<br>`;
        }
        if (contract.openInterest > 0) {
            metaInfo += `未平倉: ${contract.openInterest.toLocaleString()}`;
        }

        html += `
            <div class="contract-item ${isCurrent ? 'current' : ''} ${isRecommended ? 'recommended' : ''}"
                 data-contract="${contract.code}">
                <div class="contract-info">
                    <div class="contract-code">${contract.code}</div>
                    <div class="contract-name">${contract.name}</div>
                    <div class="contract-badges">${badges}</div>
                </div>
                <div class="contract-meta">${metaInfo}</div>
            </div>
        `;
    });

    contractList.innerHTML = html;

    // 添加點擊事件
    contractList.querySelectorAll('.contract-item').forEach(item => {
        item.addEventListener('click', function() {
            const contractCode = this.dataset.contract;
            selectContract(contractCode);
        });
    });
}

// 選擇合約
function selectContract(contractCode) {
    console.log('🔄 選擇合約:', contractCode);

    // 發送合約切換請求
    socket.emit('change_contract', contractCode);
}

// 更新當前合約顯示
function updateCurrentContract() {
    if (currentContract) {
        const contractElement = document.getElementById('contract-code');
        contractElement.textContent = `${currentContract.code} (${currentContract.name})`;
        contractElement.title = `小型恆生指數期貨 - ${currentContract.name}`;
    }
}

// 重新搜尋合約
function refreshContractList() {
    const contractList = document.getElementById('contract-list');

    contractList.innerHTML = `
        <div class="loading-contracts">
            <div class="spinner-small"></div>
            <p>正在重新搜尋合約...</p>
        </div>
    `;

    // 請求重新搜尋
    socket.emit('get_contracts');
}

// 過濾合約
function filterContracts(searchTerm) {
    const items = document.querySelectorAll('.contract-item');
    const term = searchTerm.toLowerCase();

    items.forEach(item => {
        const code = item.querySelector('.contract-code').textContent.toLowerCase();
        const name = item.querySelector('.contract-name').textContent.toLowerCase();

        if (code.includes(term) || name.includes(term)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}
