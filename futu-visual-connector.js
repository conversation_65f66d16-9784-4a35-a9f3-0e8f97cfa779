const WebSocket = require('ws');
const net = require('net');

// FUTU OpenD 可視化模式連接器
class FutuVisualConnector {
    constructor(config) {
        this.config = config;
        this.ws = null;
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxAttempts = 5;
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        this.onData = null;
    }

    async connect() {
        console.log('🔍 開始 FUTU OpenD 可視化模式連接診斷...');
        
        // 1. 檢查端口可用性
        const portAvailable = await this.checkPort();
        if (!portAvailable) {
            console.log('❌ FUTU OpenD 端口不可用');
            return false;
        }

        // 2. 嘗試不同的連接方式
        const connectionMethods = [
            () => this.connectWebSocket(),
            () => this.connectWebSocketWithAuth(),
            () => this.connectTCP(),
            () => this.connectHTTP()
        ];

        for (let i = 0; i < connectionMethods.length; i++) {
            console.log(`🔄 嘗試連接方法 ${i + 1}/${connectionMethods.length}...`);
            
            try {
                const success = await connectionMethods[i]();
                if (success) {
                    console.log(`✅ 連接成功！使用方法 ${i + 1}`);
                    return true;
                }
            } catch (error) {
                console.log(`⚠️  方法 ${i + 1} 失敗:`, error.message);
            }
            
            await this.delay(1000); // 等待1秒再嘗試下一個方法
        }

        console.log('❌ 所有連接方法都失敗了');
        return false;
    }

    async checkPort() {
        return new Promise((resolve) => {
            console.log(`🔍 檢查端口 ${this.config.host}:${this.config.port}...`);
            
            const socket = new net.Socket();
            const timeout = 3000;

            socket.setTimeout(timeout);
            
            socket.on('connect', () => {
                console.log('✅ 端口可達');
                socket.destroy();
                resolve(true);
            });

            socket.on('timeout', () => {
                console.log('⏰ 端口檢查超時');
                socket.destroy();
                resolve(false);
            });

            socket.on('error', (err) => {
                console.log('❌ 端口不可達:', err.message);
                resolve(false);
            });

            socket.connect(this.config.port, this.config.host);
        });
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            console.log('🔗 嘗試標準 WebSocket 連接...');
            
            const wsUrl = `ws://${this.config.host}:${this.config.port}`;
            this.ws = new WebSocket(wsUrl);

            const timeout = setTimeout(() => {
                this.ws.close();
                reject(new Error('WebSocket 連接超時'));
            }, 5000);

            this.ws.on('open', () => {
                clearTimeout(timeout);
                console.log('✅ WebSocket 連接建立');
                this.isConnected = true;
                
                // 發送測試消息
                this.sendTestMessage();
                
                if (this.onConnect) this.onConnect();
                resolve(true);
            });

            this.ws.on('message', (data) => {
                console.log('📨 收到數據:', data.toString().substring(0, 100) + '...');
                if (this.onData) this.onData(data);
            });

            this.ws.on('close', () => {
                clearTimeout(timeout);
                console.log('🔌 WebSocket 連接關閉');
                this.isConnected = false;
                if (this.onDisconnect) this.onDisconnect();
            });

            this.ws.on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    async connectWebSocketWithAuth() {
        return new Promise((resolve, reject) => {
            console.log('🔐 嘗試帶認證的 WebSocket 連接...');
            
            const wsUrl = `ws://${this.config.host}:${this.config.port}`;
            const headers = {
                'Authorization': `Bearer ${this.config.websocketKey}`,
                'X-Futu-Account': this.config.futuAccount,
                'X-Futu-Password': this.config.password
            };

            this.ws = new WebSocket(wsUrl, { headers });

            const timeout = setTimeout(() => {
                this.ws.close();
                reject(new Error('認證 WebSocket 連接超時'));
            }, 5000);

            this.ws.on('open', () => {
                clearTimeout(timeout);
                console.log('✅ 認證 WebSocket 連接建立');
                this.isConnected = true;
                
                // 發送認證消息
                this.sendAuthMessage();
                
                if (this.onConnect) this.onConnect();
                resolve(true);
            });

            this.ws.on('message', (data) => {
                console.log('📨 收到認證數據:', data.toString().substring(0, 100) + '...');
                if (this.onData) this.onData(data);
            });

            this.ws.on('close', () => {
                clearTimeout(timeout);
                this.isConnected = false;
                if (this.onDisconnect) this.onDisconnect();
            });

            this.ws.on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    async connectTCP() {
        return new Promise((resolve, reject) => {
            console.log('🔗 嘗試 TCP 連接...');
            
            const socket = new net.Socket();
            
            const timeout = setTimeout(() => {
                socket.destroy();
                reject(new Error('TCP 連接超時'));
            }, 5000);

            socket.connect(this.config.port, this.config.host, () => {
                clearTimeout(timeout);
                console.log('✅ TCP 連接建立');
                this.isConnected = true;
                
                // 發送 TCP 測試消息
                socket.write('PING\n');
                
                if (this.onConnect) this.onConnect();
                resolve(true);
            });

            socket.on('data', (data) => {
                console.log('📨 收到 TCP 數據:', data.toString().substring(0, 100) + '...');
                if (this.onData) this.onData(data);
            });

            socket.on('close', () => {
                clearTimeout(timeout);
                this.isConnected = false;
                if (this.onDisconnect) this.onDisconnect();
            });

            socket.on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    async connectHTTP() {
        return new Promise((resolve, reject) => {
            console.log('🌐 嘗試 HTTP 連接...');
            
            const http = require('http');
            const options = {
                hostname: this.config.host,
                port: this.config.port,
                path: '/',
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                console.log(`✅ HTTP 響應: ${res.statusCode}`);
                
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    console.log('📨 HTTP 數據:', data.substring(0, 100) + '...');
                    if (this.onData) this.onData(data);
                    resolve(true);
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('HTTP 連接超時'));
            });

            req.end();
        });
    }

    sendTestMessage() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            const testMsg = JSON.stringify({
                cmd: 'ping',
                timestamp: Date.now(),
                account: this.config.futuAccount
            });
            
            console.log('📤 發送測試消息:', testMsg);
            this.ws.send(testMsg);
        }
    }

    sendAuthMessage() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            const authMsg = JSON.stringify({
                cmd: 'auth',
                account: this.config.futuAccount,
                password: this.config.password,
                key: this.config.websocketKey,
                timestamp: Date.now()
            });
            
            console.log('🔐 發送認證消息');
            this.ws.send(authMsg);
        }
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
    }

    // 訂閱期貨合約數據
    async subscribeToContract(contractCode) {
        try {
            console.log(`📡 正在訂閱期貨合約: ${contractCode}`);

            if (this.ws && this.ws.readyState === 1) {
                // 使用 FUTU API 的正確訂閱格式
                const subscribeRequest = {
                    Protocol: "Qot_Sub",
                    Version: 1,
                    ReqParam: {
                        c2s: {
                            securityList: [
                                {
                                    market: 2, // QotMarket_HK_Future = 2 (香港期貨市場)
                                    code: contractCode
                                }
                            ],
                            subTypeList: [
                                1, // SubType_Basic = 1 (基本報價)
                                6, // SubType_Ticker = 6 (逐筆)
                                3  // SubType_OrderBook = 3 (買賣盤)
                            ],
                            isSubOrUnSub: true, // true = 訂閱
                            isRegOrUnRegPush: true, // 註冊推送
                            isFirstPush: true // 立即推送一次緩存數據
                        }
                    }
                };

                console.log('📤 發送訂閱請求:', JSON.stringify(subscribeRequest, null, 2));
                this.ws.send(JSON.stringify(subscribeRequest));
                console.log(`✅ 已發送訂閱請求: ${contractCode}`);

                return true;
            } else {
                console.log('❌ WebSocket 未連接，無法訂閱');
                return false;
            }

        } catch (error) {
            console.log('❌ 訂閱合約失敗:', error.message);
            return false;
        }
    }

    // 取消訂閱期貨合約
    async unsubscribeFromContract(contractCode) {
        try {
            console.log(`📡 正在取消訂閱期貨合約: ${contractCode}`);

            if (this.ws && this.ws.readyState === 1) {
                const unsubscribeRequest = {
                    Protocol: "Qot_Sub",
                    Version: 1,
                    ReqParam: {
                        c2s: {
                            securityList: [
                                {
                                    market: 2, // QotMarket_HK_Future = 2
                                    code: contractCode
                                }
                            ],
                            subTypeList: [1, 6, 3],
                            isSubOrUnSub: false, // false = 取消訂閱
                            isRegOrUnRegPush: false
                        }
                    }
                };

                this.ws.send(JSON.stringify(unsubscribeRequest));
                console.log(`✅ 已取消訂閱: ${contractCode}`);

                return true;
            } else {
                console.log('❌ WebSocket 未連接，無法取消訂閱');
                return false;
            }

        } catch (error) {
            console.log('❌ 取消訂閱失敗:', error.message);
            return false;
        }
    }

    // 診斷 FUTU OpenD 狀態
    async diagnose() {
        console.log('🔍 FUTU OpenD 可視化模式診斷');
        console.log('================================');
        
        // 檢查常見端口
        const commonPorts = [33333, 11111, 22222, 8888, 9999];
        
        for (const port of commonPorts) {
            console.log(`🔍 檢查端口 ${port}...`);
            const available = await this.checkPortDirect(port);
            console.log(`   端口 ${port}: ${available ? '✅ 開放' : '❌ 關閉'}`);
        }

        console.log('');
        console.log('💡 可視化模式建議:');
        console.log('1. 確認 FUTU OpenD 已啟動');
        console.log('2. 檢查 OpenD 設置中的 API 配置');
        console.log('3. 確認 WebSocket 服務已啟用');
        console.log('4. 檢查防火牆設置');
        console.log('5. 嘗試重啟 FUTU OpenD');
    }

    async checkPortDirect(port) {
        return new Promise((resolve) => {
            const socket = new net.Socket();
            socket.setTimeout(1000);
            
            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });

            socket.on('timeout', () => {
                socket.destroy();
                resolve(false);
            });

            socket.on('error', () => {
                resolve(false);
            });

            socket.connect(port, this.config.host);
        });
    }
}

module.exports = FutuVisualConnector;
