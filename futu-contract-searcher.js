const FutuVisualConnector = require('./futu-visual-connector');

// FUTU 合約搜尋器
class FutuContractSearcher {
    constructor(config) {
        this.config = config;
        this.connector = null;
        this.availableContracts = [];
    }

    async searchMHIContracts() {
        console.log('🔍 正在搜尋 FUTU 內現有的小型恆生指數期貨合約...');
        
        try {
            // 創建連接器
            this.connector = new FutuVisualConnector(this.config);
            
            // 設置事件監聽
            this.connector.onConnect = () => {
                console.log('✅ 已連接到 FUTU OpenD，開始搜尋合約...');
            };

            this.connector.onData = (data) => {
                this.parseContractData(data);
            };

            // 嘗試連接
            const connected = await this.connector.connect();
            
            if (connected) {
                // 搜尋 MHI 合約
                await this.requestMHIContracts();
                
                // 等待數據返回
                await this.waitForContractData();
                
                this.connector.disconnect();
                
                return this.availableContracts;
            } else {
                console.log('❌ 無法連接到 FUTU OpenD，使用預設合約列表');
                return this.getDefaultContracts();
            }
            
        } catch (error) {
            console.log('❌ 搜尋合約時發生錯誤:', error.message);
            console.log('🔄 使用預設合約列表');
            return this.getDefaultContracts();
        }
    }

    async requestMHIContracts() {
        // 使用 FUTU WebSocket API 的正確格式
        // 參考: https://openapi.futunn.com/futu-api-doc/quote/get-future-info.html
        const getFutureInfoRequest = {
            Protocol: "Qot_GetFutureInfo",
            Version: 1,
            ReqParam: {
                c2s: {
                    securityList: [
                        {
                            market: 1, // QotMarket_HK_Future
                            code: 'MHImain' // 小型恆生指數主連
                        },
                        {
                            market: 1,
                            code: 'HSImain' // 恆生指數主連（用於比較）
                        }
                    ]
                }
            }
        };

        if (this.connector && this.connector.ws) {
            console.log('📡 發送期貨合約信息請求...');
            console.log('📤 請求格式:', JSON.stringify(getFutureInfoRequest, null, 2));
            this.connector.ws.send(JSON.stringify(getFutureInfoRequest));
        }
    }

    parseContractData(data) {
        try {
            const response = JSON.parse(data.toString());
            console.log('📨 收到響應:', JSON.stringify(response, null, 2));

            // 檢查不同的響應格式
            let futureInfoList = [];

            if (response.Protocol === 'Qot_GetFutureInfo' && response.RetData && response.RetData.s2c) {
                futureInfoList = response.RetData.s2c.futureInfoList || [];
            } else if (response.s2c && response.s2c.futureInfoList) {
                futureInfoList = response.s2c.futureInfoList;
            } else if (response.data && response.data.s2c && response.data.s2c.futureInfoList) {
                futureInfoList = response.data.s2c.futureInfoList;
            }

            if (futureInfoList.length > 0) {
                console.log('📊 收到期貨合約數據，正在解析...');
                console.log(`📋 找到 ${futureInfoList.length} 個期貨合約`);

                futureInfoList.forEach(futureInfo => {
                    const security = futureInfo.security;
                    const originSecurity = futureInfo.origin;

                    console.log('🔍 處理合約:', security);

                    if (security && security.code) {
                        // 處理所有相關的期貨合約
                        if (security.code.includes('MHI') || security.code.includes('HSI')) {
                            const contractInfo = {
                                code: security.code,
                                name: futureInfo.name || this.parseContractNameFromCode(security.code),
                                expiry: futureInfo.lastTradeTime || '',
                                market: security.market || 1,
                                lastTrade: futureInfo.lastTradeTime || '',
                                volume: 0, // WebSocket 通常不提供實時成交量
                                openInterest: 0,
                                contractSize: futureInfo.contractSize || 10,
                                contractType: futureInfo.contractType || '股指期貨',
                                isMainContract: security.code.includes('main'),
                                originCode: originSecurity ? originSecurity.code : ''
                            };

                            this.availableContracts.push(contractInfo);
                            console.log('✅ 添加合約:', contractInfo.code, '-', contractInfo.name);

                            // 如果有實際合約代碼，也添加到列表
                            if (originSecurity && originSecurity.code && originSecurity.code !== security.code) {
                                const originContractInfo = {
                                    code: originSecurity.code,
                                    name: this.parseContractNameFromCode(originSecurity.code),
                                    expiry: futureInfo.lastTradeTime || '',
                                    market: originSecurity.market || 1,
                                    lastTrade: futureInfo.lastTradeTime || '',
                                    volume: 0,
                                    openInterest: 0,
                                    contractSize: futureInfo.contractSize || 10,
                                    contractType: futureInfo.contractType || '股指期貨',
                                    isMainContract: false
                                };

                                this.availableContracts.push(originContractInfo);
                                console.log('✅ 添加實際合約:', originContractInfo.code, '-', originContractInfo.name);
                            }
                        }
                    }
                });
            }

            // 如果沒有找到 MHI 合約，生成預設的合約列表
            if (this.availableContracts.length === 0) {
                console.log('⚠️  未找到 MHI 合約，生成預設合約列表');
                this.generateMHIContracts();
            }

            console.log(`✅ 總共找到 ${this.availableContracts.length} 個合約`);

        } catch (error) {
            console.log('⚠️  解析合約數據失敗:', error.message);
            console.log('📄 原始數據:', data.toString());
            this.generateMHIContracts();
        }
    }

    parseContractNameFromCode(code) {
        if (code === 'MHImain') {
            return '小型恆生指數主連';
        }

        if (code.match(/MHI(\d{4})/)) {
            const yearMonth = code.match(/MHI(\d{4})/)[1];
            const year = '20' + yearMonth.substring(0, 2);
            const month = parseInt(yearMonth.substring(2, 4));

            const monthNames = [
                '', '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ];

            return `${year}年${monthNames[month]}到期`;
        }

        return '未知到期時間';
    }

    generateMHIContracts() {
        console.log('📋 生成正確的小型恆生指數期貨合約列表');

        // 添加主連合約（推薦）
        this.availableContracts.push({
            code: 'MHImain',
            name: '小型恆生指數主連',
            displayName: 'MHImain (小型恆生指數主連)',
            expiry: '',
            market: 1,
            lastTrade: '',
            volume: 0, // 主連沒有直接成交量
            openInterest: 0,
            contractSize: 10,
            contractType: '股指期貨',
            tickSize: 1, // 最小變動 1 點
            tickValue: 10, // 1 點 = 10 港元
            currency: 'HKD',
            isMainContract: true,
            isRecommended: true,
            isActive: true,
            description: '自動轉換到最活躍的合約，無需手動轉倉'
        });

        // 生成當前可交易的月份合約
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        // 生成未來 12 個月的合約
        for (let i = 0; i < 12; i++) {
            const futureDate = new Date(currentYear, currentMonth - 1 + i, 1);
            const year = futureDate.getFullYear().toString().substring(2);
            const month = (futureDate.getMonth() + 1).toString().padStart(2, '0');
            const code = `MHI${year}${month}`;

            const monthNames = [
                '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ];

            // 計算到期日（合約月份倒數第二個營業日）
            const expiryDate = new Date(futureDate.getFullYear(), futureDate.getMonth() + 1, 0);
            expiryDate.setDate(expiryDate.getDate() - 2); // 簡化為月末前2天

            // 模擬流動性：近月合約流動性更好
            const isNearMonth = i < 3;
            const isQuarterMonth = [2, 5, 8, 11].includes(futureDate.getMonth()); // 3,6,9,12月

            this.availableContracts.push({
                code: code,
                name: `${futureDate.getFullYear()}年${monthNames[futureDate.getMonth()]}到期`,
                displayName: `${code} (${futureDate.getFullYear()}年${monthNames[futureDate.getMonth()]}到期)`,
                expiry: expiryDate.toISOString().split('T')[0],
                market: 1,
                lastTrade: expiryDate.toISOString().split('T')[0],
                volume: isNearMonth ? Math.max(500, 2000 - i * 300) : Math.max(50, 500 - i * 50),
                openInterest: isNearMonth ? Math.max(2000, 10000 - i * 1000) : Math.max(200, 2000 - i * 200),
                contractSize: 10,
                contractType: '股指期貨',
                tickSize: 1,
                tickValue: 10,
                currency: 'HKD',
                isMainContract: false,
                isRecommended: i === 0, // 最近月份推薦
                isActive: i < 6, // 前6個月比較活躍
                isQuarterMonth: isQuarterMonth,
                description: isNearMonth ? '近月合約，流動性較好' : '遠月合約，適合長期策略'
            });
        }

        console.log(`✅ 生成了 ${this.availableContracts.length} 個 MHI 合約`);
    }

    async waitForContractData() {
        return new Promise((resolve) => {
            // 等待 3 秒收集數據
            setTimeout(() => {
                resolve();
            }, 3000);
        });
    }

    getDefaultContracts() {
        console.log('📋 使用預設合約列表');

        // 清空現有合約列表
        this.availableContracts = [];

        // 生成預設合約
        this.generateMHIContracts();

        return this.availableContracts;
    }

    // 獲取合約的詳細信息
    async getContractDetails(contractCode) {
        try {
            if (!this.connector) {
                this.connector = new FutuVisualConnector(this.config);
            }

            const connected = await this.connector.connect();
            
            if (connected) {
                const detailRequest = {
                    cmd: 'get_contract_info',
                    data: {
                        symbol: contractCode,
                        market: 1
                    }
                };

                this.connector.ws.send(JSON.stringify(detailRequest));
                
                // 等待響應
                return new Promise((resolve) => {
                    const originalOnData = this.connector.onData;
                    
                    this.connector.onData = (data) => {
                        try {
                            const response = JSON.parse(data.toString());
                            if (response.cmd === 'get_contract_info') {
                                resolve(response.data);
                                this.connector.onData = originalOnData;
                            }
                        } catch (error) {
                            resolve(null);
                        }
                    };
                    
                    // 3秒超時
                    setTimeout(() => {
                        resolve(null);
                        this.connector.onData = originalOnData;
                    }, 3000);
                });
            }
            
            return null;
        } catch (error) {
            console.log('❌ 獲取合約詳情失敗:', error.message);
            return null;
        }
    }

    // 驗證合約是否有效
    async validateContract(contractCode) {
        const contracts = await this.searchMHIContracts();
        return contracts.find(contract => contract.code === contractCode);
    }

    // 獲取推薦合約（流動性最高的）
    getRecommendedContract(contracts) {
        if (!contracts || contracts.length === 0) {
            return null;
        }

        // 優先推薦主連合約
        const mainContract = contracts.find(contract => contract.isMainContract);
        if (mainContract) {
            return mainContract;
        }

        // 按成交量排序，選擇流動性最高的
        const sortedByVolume = contracts
            .filter(contract => contract.volume > 0 && !contract.isMainContract)
            .sort((a, b) => b.volume - a.volume);

        if (sortedByVolume.length > 0) {
            return sortedByVolume[0];
        }

        // 如果沒有成交量數據，選擇最近月份的合約
        const sortedByExpiry = contracts
            .filter(contract => contract.expiry && !contract.isMainContract)
            .sort((a, b) => new Date(a.expiry) - new Date(b.expiry));

        return sortedByExpiry[0] || contracts[0];
    }

    // 驗證合約代碼是否有效
    isValidMHIContract(code) {
        // 主連合約
        if (code === 'MHImain') return true;

        // 月份合約格式: MHI + 年份(2位) + 月份(2位)
        const pattern = /^MHI\d{4}$/;
        if (!pattern.test(code)) return false;

        const yearMonth = code.substring(3);
        const year = parseInt('20' + yearMonth.substring(0, 2));
        const month = parseInt(yearMonth.substring(2, 4));

        // 檢查年份和月份的合理性
        const currentYear = new Date().getFullYear();
        return year >= currentYear &&
               year <= currentYear + 2 &&
               month >= 1 &&
               month <= 12;
    }

    // 獲取合約的詳細描述
    getContractDescription(contract) {
        if (contract.isMainContract) {
            return '主連合約 - 自動轉換到最活躍的合約，無需手動轉倉，推薦使用';
        }

        let description = contract.description || '';

        if (contract.isActive) {
            description += ' • 流動性較好';
        }

        if (contract.isQuarterMonth) {
            description += ' • 季月合約';
        }

        if (contract.volume > 1000) {
            description += ' • 成交活躍';
        }

        return description || '期貨合約';
    }

    // 格式化合約列表供前端使用
    formatContractsForFrontend(contracts) {
        return contracts.map(contract => ({
            code: contract.code,
            name: contract.name,
            displayName: contract.displayName || `${contract.code} (${contract.name})`,
            expiry: contract.expiry,
            volume: contract.volume,
            openInterest: contract.openInterest,
            contractSize: contract.contractSize,
            tickSize: contract.tickSize,
            tickValue: contract.tickValue,
            currency: contract.currency,
            isActive: contract.isActive || contract.volume > 0,
            isRecommended: contract.isRecommended,
            isMainContract: contract.isMainContract,
            isQuarterMonth: contract.isQuarterMonth,
            description: this.getContractDescription(contract)
        }));
    }
}

module.exports = FutuContractSearcher;
