const FutuVisualConnector = require('./futu-visual-connector');

// FUTU 合約搜尋器
class FutuContractSearcher {
    constructor(config) {
        this.config = config;
        this.connector = null;
        this.availableContracts = [];
    }

    async searchMHIContracts() {
        console.log('🔍 正在搜尋 FUTU 內現有的小型恆生指數期貨合約...');
        
        try {
            // 創建連接器
            this.connector = new FutuVisualConnector(this.config);
            
            // 設置事件監聽
            this.connector.onConnect = () => {
                console.log('✅ 已連接到 FUTU OpenD，開始搜尋合約...');
            };

            this.connector.onData = (data) => {
                this.parseContractData(data);
            };

            // 嘗試連接
            const connected = await this.connector.connect();
            
            if (connected) {
                // 搜尋 MHI 合約
                await this.requestMHIContracts();
                
                // 等待數據返回
                await this.waitForContractData();
                
                this.connector.disconnect();
                
                return this.availableContracts;
            } else {
                console.log('❌ 無法連接到 FUTU OpenD，使用預設合約列表');
                return this.getDefaultContracts();
            }
            
        } catch (error) {
            console.log('❌ 搜尋合約時發生錯誤:', error.message);
            console.log('🔄 使用預設合約列表');
            return this.getDefaultContracts();
        }
    }

    async requestMHIContracts() {
        // 構建搜尋請求
        const searchRequest = {
            cmd: 'search_contracts',
            data: {
                market: 1, // 香港市場
                secType: 'FUT', // 期貨
                symbol: 'MHI', // 小型恆生指數
                currency: 'HKD'
            }
        };

        if (this.connector && this.connector.ws) {
            console.log('📡 發送合約搜尋請求...');
            this.connector.ws.send(JSON.stringify(searchRequest));
        }
    }

    parseContractData(data) {
        try {
            const response = JSON.parse(data.toString());
            
            if (response.cmd === 'search_contracts' && response.data) {
                console.log('📊 收到合約數據，正在解析...');
                
                const contracts = response.data.contracts || [];
                
                contracts.forEach(contract => {
                    if (contract.symbol && contract.symbol.startsWith('MHI')) {
                        this.availableContracts.push({
                            code: contract.symbol,
                            name: this.parseContractName(contract),
                            expiry: contract.expiry || '',
                            market: contract.market || 1,
                            lastTrade: contract.lastTrade || '',
                            volume: contract.volume || 0,
                            openInterest: contract.openInterest || 0
                        });
                    }
                });
                
                console.log(`✅ 找到 ${this.availableContracts.length} 個 MHI 合約`);
            }
        } catch (error) {
            console.log('⚠️  解析合約數據失敗:', error.message);
        }
    }

    parseContractName(contract) {
        const code = contract.symbol;
        
        if (code.match(/MHI(\d{4})/)) {
            const yearMonth = code.match(/MHI(\d{4})/)[1];
            const year = '20' + yearMonth.substring(0, 2);
            const month = parseInt(yearMonth.substring(2, 4));
            
            const monthNames = [
                '', '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ];
            
            return `${year}年${monthNames[month]}到期`;
        }
        
        return contract.description || '未知到期時間';
    }

    async waitForContractData() {
        return new Promise((resolve) => {
            // 等待 3 秒收集數據
            setTimeout(() => {
                resolve();
            }, 3000);
        });
    }

    getDefaultContracts() {
        console.log('📋 使用預設合約列表');
        
        // 生成未來 12 個月的合約
        const contracts = [];
        const currentDate = new Date();
        
        for (let i = 0; i < 12; i++) {
            const futureDate = new Date(currentDate);
            futureDate.setMonth(currentDate.getMonth() + i);
            
            const year = futureDate.getFullYear().toString().substring(2);
            const month = (futureDate.getMonth() + 1).toString().padStart(2, '0');
            const code = `MHI${year}${month}`;
            
            const monthNames = [
                '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ];
            
            contracts.push({
                code: code,
                name: `${futureDate.getFullYear()}年${monthNames[futureDate.getMonth()]}到期`,
                expiry: futureDate.toISOString().split('T')[0],
                market: 1,
                lastTrade: '',
                volume: 0,
                openInterest: 0,
                isDefault: true
            });
        }
        
        return contracts;
    }

    // 獲取合約的詳細信息
    async getContractDetails(contractCode) {
        try {
            if (!this.connector) {
                this.connector = new FutuVisualConnector(this.config);
            }

            const connected = await this.connector.connect();
            
            if (connected) {
                const detailRequest = {
                    cmd: 'get_contract_info',
                    data: {
                        symbol: contractCode,
                        market: 1
                    }
                };

                this.connector.ws.send(JSON.stringify(detailRequest));
                
                // 等待響應
                return new Promise((resolve) => {
                    const originalOnData = this.connector.onData;
                    
                    this.connector.onData = (data) => {
                        try {
                            const response = JSON.parse(data.toString());
                            if (response.cmd === 'get_contract_info') {
                                resolve(response.data);
                                this.connector.onData = originalOnData;
                            }
                        } catch (error) {
                            resolve(null);
                        }
                    };
                    
                    // 3秒超時
                    setTimeout(() => {
                        resolve(null);
                        this.connector.onData = originalOnData;
                    }, 3000);
                });
            }
            
            return null;
        } catch (error) {
            console.log('❌ 獲取合約詳情失敗:', error.message);
            return null;
        }
    }

    // 驗證合約是否有效
    async validateContract(contractCode) {
        const contracts = await this.searchMHIContracts();
        return contracts.find(contract => contract.code === contractCode);
    }

    // 獲取推薦合約（流動性最高的）
    getRecommendedContract(contracts) {
        if (!contracts || contracts.length === 0) {
            return null;
        }

        // 按成交量排序，選擇流動性最高的
        const sortedByVolume = contracts
            .filter(contract => contract.volume > 0)
            .sort((a, b) => b.volume - a.volume);

        if (sortedByVolume.length > 0) {
            return sortedByVolume[0];
        }

        // 如果沒有成交量數據，選擇最近月份的合約
        const sortedByExpiry = contracts
            .filter(contract => contract.expiry)
            .sort((a, b) => new Date(a.expiry) - new Date(b.expiry));

        return sortedByExpiry[0] || contracts[0];
    }

    // 格式化合約列表供前端使用
    formatContractsForFrontend(contracts) {
        return contracts.map(contract => ({
            code: contract.code,
            name: contract.name,
            displayName: `${contract.code} (${contract.name})`,
            expiry: contract.expiry,
            volume: contract.volume,
            openInterest: contract.openInterest,
            isActive: contract.volume > 0,
            isRecommended: false // 將在後續設置
        }));
    }
}

module.exports = FutuContractSearcher;
