const readline = require('readline');
const { spawn } = require('child_process');

// 小型恆生指數期貨合約選項
const MHI_CONTRACTS = {
    '1': { code: 'MHI2501', name: '2025年1月到期', description: '最近月份，流動性最高' },
    '2': { code: 'MHI2502', name: '2025年2月到期', description: '次近月份' },
    '3': { code: 'MHI2503', name: '2025年3月到期', description: '季月合約' },
    '4': { code: 'MHI2504', name: '2025年4月到期', description: '遠月合約' },
    '5': { code: 'MHI2505', name: '2025年5月到期', description: '遠月合約' },
    '6': { code: 'MHI2506', name: '2025年6月到期', description: '季月合約' }
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function showContractMenu() {
    console.log('');
    console.log('📊 小型恆生指數期貨合約選擇');
    console.log('=====================================');
    console.log('');
    
    Object.keys(MHI_CONTRACTS).forEach(key => {
        const contract = MHI_CONTRACTS[key];
        console.log(`${key}. ${contract.code} - ${contract.name}`);
        console.log(`   ${contract.description}`);
        console.log('');
    });
    
    console.log('💡 建議選擇最近月份合約 (選項1) 以獲得最佳流動性');
    console.log('');
}

function promptContractSelection() {
    return new Promise((resolve) => {
        rl.question('請選擇合約 (1-6，預設為1): ', (answer) => {
            const choice = answer.trim() || '1';
            
            if (MHI_CONTRACTS[choice]) {
                const selected = MHI_CONTRACTS[choice];
                console.log('');
                console.log(`✅ 已選擇: ${selected.code} (${selected.name})`);
                resolve(selected.code);
            } else {
                console.log('❌ 無效選項，使用預設合約 MHI2501');
                resolve('MHI2501');
            }
        });
    });
}

function showStartupOptions() {
    console.log('');
    console.log('🚀 啟動選項:');
    console.log('1. 啟動網頁版追蹤器');
    console.log('2. 啟動控制台版追蹤器');
    console.log('3. 運行連接診斷');
    console.log('4. 重新選擇合約');
    console.log('5. 退出');
    console.log('');
}

function promptStartupOption(contract) {
    return new Promise((resolve) => {
        rl.question('請選擇啟動選項 (1-5): ', (answer) => {
            const choice = answer.trim();
            
            switch (choice) {
                case '1':
                    console.log('🌐 啟動網頁版追蹤器...');
                    startWebTracker(contract);
                    resolve();
                    break;
                case '2':
                    console.log('💻 啟動控制台版追蹤器...');
                    startConsoleTracker(contract);
                    resolve();
                    break;
                case '3':
                    console.log('🔍 運行連接診斷...');
                    runDiagnostic();
                    resolve();
                    break;
                case '4':
                    main(); // 重新開始
                    resolve();
                    break;
                case '5':
                    console.log('👋 再見！');
                    rl.close();
                    process.exit(0);
                    break;
                default:
                    console.log('❌ 無效選項，請重新選擇');
                    promptStartupOption(contract).then(resolve);
                    break;
            }
        });
    });
}

function startWebTracker(contract) {
    console.log(`📊 使用合約: ${contract}`);
    console.log('🌐 正在啟動網頁版...');
    
    const child = spawn('node', ['app.js', `--contract=${contract}`], {
        stdio: 'inherit',
        shell: true
    });
    
    child.on('close', (code) => {
        console.log(`\n程序已退出，代碼: ${code}`);
        rl.close();
    });
}

function startConsoleTracker(contract) {
    console.log(`📊 使用合約: ${contract}`);
    console.log('💻 正在啟動控制台版...');
    
    // 更新 index.js 中的合約設置
    updateConsoleContract(contract);
    
    const child = spawn('node', ['index.js'], {
        stdio: 'inherit',
        shell: true
    });
    
    child.on('close', (code) => {
        console.log(`\n程序已退出，代碼: ${code}`);
        rl.close();
    });
}

function updateConsoleContract(contract) {
    const fs = require('fs');
    
    try {
        let content = fs.readFileSync('index.js', 'utf8');
        
        // 替換合約代碼
        content = content.replace(
            /code: 'MHI\d{4}'/,
            `code: '${contract}'`
        );
        
        fs.writeFileSync('index.js', content);
        console.log(`✅ 已更新控制台版合約為: ${contract}`);
    } catch (error) {
        console.log('⚠️  無法更新控制台版合約設置');
    }
}

function runDiagnostic() {
    const child = spawn('node', ['futu-visual-diagnostic.js'], {
        stdio: 'inherit',
        shell: true
    });
    
    child.on('close', (code) => {
        console.log(`\n診斷完成，代碼: ${code}`);
        console.log('按任意鍵繼續...');
        
        rl.question('', () => {
            main();
        });
    });
}

async function main() {
    console.clear();
    console.log('🎯 恆生指數期貨實時追蹤器');
    console.log('============================');
    
    showContractMenu();
    const selectedContract = await promptContractSelection();
    
    showStartupOptions();
    await promptStartupOption(selectedContract);
}

// 處理程序退出
process.on('SIGINT', () => {
    console.log('\n👋 再見！');
    rl.close();
    process.exit(0);
});

// 啟動程序
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序錯誤:', error);
        rl.close();
        process.exit(1);
    });
}

module.exports = { MHI_CONTRACTS };
