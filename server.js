const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const FutuAPI = require('./futu-api');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 配置信息
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',
    futuAccount: '********',
    password: 'leotam060690'
};

// 恆生指數期貨代碼
const HSI_FUTURES = {
    market: 1,
    code: 'MHI2501'
};

// 靜態文件服務
app.use(express.static(path.join(__dirname, 'public')));

// 主頁路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

class WebFuturesTracker {
    constructor() {
        this.futuAPI = null;
        this.isConnected = false;
        this.lastPrice = null;
        this.priceHistory = [];
        this.clients = new Set();
        this.heartbeatInterval = null;
        this.dataInterval = null;
        this.useRealData = false;
    }

    start() {
        console.log('🚀 啟動網頁版恆生指數期貨追蹤器...');
        console.log(`📊 目標合約: ${HSI_FUTURES.code}`);
        console.log(`🔑 使用帳號: ${CONFIG.futuAccount}`);
        
        this.setupSocketIO();
        this.connectToFutu();
    }

    setupSocketIO() {
        io.on('connection', (socket) => {
            console.log('👤 新用戶連接:', socket.id);
            this.clients.add(socket);

            // 發送當前狀態
            socket.emit('status', {
                connected: this.isConnected,
                contract: HSI_FUTURES.code,
                account: CONFIG.futuAccount
            });

            // 發送歷史數據
            if (this.priceHistory.length > 0) {
                socket.emit('history', this.priceHistory.slice(-50));
            }

            // 處理客戶端斷開
            socket.on('disconnect', () => {
                console.log('👤 用戶斷開:', socket.id);
                this.clients.delete(socket);
            });

            // 處理重新連接請求
            socket.on('reconnect_request', () => {
                this.connectToFutu();
            });
        });
    }

    async connectToFutu() {
        try {
            console.log(`🔗 正在連接到 FUTU OpenD: ${CONFIG.host}:${CONFIG.port}`);
            console.log(`👤 使用帳號: ${CONFIG.futuAccount}`);

            // 創建 FUTU API 實例
            this.futuAPI = new FutuAPI();

            // 設置事件監聽器
            this.futuAPI.onConnect = () => {
                console.log('✅ FUTU API 連接成功');
                this.isConnected = true;
                this.useRealData = true;
                this.broadcastStatus();
                this.subscribeToFutures();
            };

            this.futuAPI.onDisconnect = () => {
                console.log('❌ FUTU API 連接斷開');
                this.isConnected = false;
                this.useRealData = false;
                this.broadcastStatus();
                this.startDataSimulation(); // 回退到模擬數據
            };

            this.futuAPI.onError = (error) => {
                console.error('🚨 FUTU API 錯誤:', error.message);
                this.isConnected = false;
                this.useRealData = false;
                this.broadcastStatus();
                this.startDataSimulation(); // 回退到模擬數據

                // 5秒後重試連接
                setTimeout(() => {
                    this.connectToFutu();
                }, 5000);
            };

            this.futuAPI.onQuotePush = (data) => {
                this.handleRealQuoteData(data);
            };

            // 嘗試連接
            await this.futuAPI.connect(
                CONFIG.host,
                CONFIG.port,
                CONFIG.enableSSL,
                CONFIG.websocketKey,
                CONFIG.futuAccount,
                CONFIG.password
            );

        } catch (error) {
            console.error('❌ 連接失敗:', error.message);
            console.log('🔄 回退到模擬數據模式');
            this.isConnected = false;
            this.useRealData = false;
            this.broadcastStatus();
            this.startDataSimulation();
        }
    }

    async subscribeToFutures() {
        try {
            console.log(`📈 正在訂閱 ${HSI_FUTURES.code} 期貨數據...`);

            // 訂閱基礎行情
            const subRequest = {
                c2s: {
                    securityList: [HSI_FUTURES],
                    subTypeList: [1], // 基礎報價
                    isSubOrUnSub: true,
                    isRegOrUnRegPush: true
                }
            };

            await this.futuAPI.subscribe(subRequest);
            console.log(`✅ 已訂閱 ${HSI_FUTURES.code} 實時行情`);

            // 獲取初始快照
            await this.getInitialSnapshot();

        } catch (error) {
            console.error('❌ 訂閱失敗:', error.message);
            console.log('🔄 回退到模擬數據模式');
            this.startDataSimulation();
        }
    }

    async getInitialSnapshot() {
        try {
            const snapshotRequest = {
                c2s: {
                    securityList: [HSI_FUTURES]
                }
            };

            const response = await this.futuAPI.getSecuritySnapshot(snapshotRequest);

            if (response && response.s2c && response.s2c.snapshotList) {
                const snapshot = response.s2c.snapshotList[0];
                if (snapshot && snapshot.basic) {
                    console.log('📊 收到初始快照數據');
                    this.handleRealQuoteData({ s2c: { securityList: [{ basic: snapshot.basic }] } });
                }
            }
        } catch (error) {
            console.error('❌ 獲取快照失敗:', error.message);
        }
    }

    handleRealQuoteData(data) {
        try {
            if (data && data.s2c && data.s2c.securityList) {
                const quote = data.s2c.securityList[0];
                if (quote && quote.basic) {
                    const basic = quote.basic;

                    const priceData = {
                        timestamp: new Date().toISOString(),
                        price: parseFloat(basic.curPrice || this.lastPrice || 19850),
                        volume: parseInt(basic.volume || 0),
                        change: parseFloat(basic.changeVal || 0),
                        changePercent: parseFloat(basic.changeRate ? basic.changeRate * 100 : 0)
                    };

                    console.log(`📈 真實數據: ${priceData.price} (${priceData.change >= 0 ? '+' : ''}${priceData.change})`);

                    this.lastPrice = priceData.price;
                    this.priceHistory.push(priceData);

                    // 保持最近1000筆記錄
                    if (this.priceHistory.length > 1000) {
                        this.priceHistory.shift();
                    }

                    // 廣播給所有客戶端
                    this.broadcastPriceUpdate(priceData);
                }
            }
        } catch (error) {
            console.error('❌ 處理真實數據失敗:', error);
        }
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const heartbeat = JSON.stringify({ type: 'ping', timestamp: Date.now() });
                this.ws.send(heartbeat);
            }
        }, 30000);
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    startDataSimulation() {
        // 只在沒有真實數據時啟動模擬
        if (this.useRealData) {
            return;
        }

        console.log('📈 開始模擬實時數據...');
        console.log('⚠️  注意: 這是模擬數據，等待 FUTU API 連接...');

        let basePrice = this.lastPrice || 19850;
        let volume = 12500;

        this.dataInterval = setInterval(() => {
            // 如果已經有真實數據，停止模擬
            if (this.useRealData) {
                this.stopDataSimulation();
                return;
            }

            // 模擬價格波動
            const change = (Math.random() - 0.5) * 20;
            basePrice += change;
            volume += Math.floor(Math.random() * 100);

            const priceData = {
                timestamp: new Date().toISOString(),
                price: parseFloat(basePrice.toFixed(2)),
                volume: volume,
                change: parseFloat(change.toFixed(2)),
                changePercent: parseFloat(((change / (basePrice - change)) * 100).toFixed(2)),
                isSimulated: true
            };

            this.lastPrice = priceData.price;
            this.priceHistory.push(priceData);

            // 保持最近1000筆記錄
            if (this.priceHistory.length > 1000) {
                this.priceHistory.shift();
            }

            // 廣播給所有客戶端
            this.broadcastPriceUpdate(priceData);

        }, 1000); // 每秒更新
    }

    stopDataSimulation() {
        if (this.dataInterval) {
            clearInterval(this.dataInterval);
            this.dataInterval = null;
        }
    }

    broadcastStatus() {
        const status = {
            connected: this.isConnected,
            contract: HSI_FUTURES.code,
            account: CONFIG.futuAccount,
            timestamp: new Date().toISOString(),
            dataType: this.useRealData ? 'real' : 'simulated'
        };

        io.emit('status', status);
    }

    broadcastPriceUpdate(priceData) {
        io.emit('price_update', priceData);
    }

    cleanup() {
        this.stopDataSimulation();
        if (this.futuAPI) {
            this.futuAPI.disconnect();
            this.futuAPI = null;
        }
        this.isConnected = false;
        this.useRealData = false;
    }
}

// 創建追蹤器實例
const tracker = new WebFuturesTracker();

// 啟動服務器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🌐 網頁服務器啟動在端口 ${PORT}`);
    console.log(`📱 請在瀏覽器中訪問: http://localhost:${PORT}`);
    tracker.start();
});

// 優雅關閉
process.on('SIGINT', () => {
    console.log('\n🛑 正在關閉服務器...');
    tracker.cleanup();
    server.close(() => {
        console.log('✅ 服務器已關閉');
        process.exit(0);
    });
});
