# 恆生指數期貨實時價格追蹤器

這是一個使用 Node.js 和 FUTU API 來實時取得恆生指數期貨價格的程式，現在提供**網頁版**和**控制台版**兩種使用方式。

## 🌟 版本選擇

### 🌐 網頁版（推薦）
- 現代化的網頁界面
- 實時圖表和數據可視化
- 響應式設計，支持手機和電腦
- 互動式控制面板

### 💻 控制台版
- 傳統的命令行界面
- 輕量級，資源占用少
- 適合服務器環境運行

## 功能特色

- 🔄 實時接收恆生指數期貨價格更新
- 📊 顯示詳細的市場數據（開盤價、最高價、最低價、成交量等）
- 📈 價格變化追蹤和歷史記錄
- 🔔 清晰的控制台輸出格式
- ⚡ 1秒級實時更新

## 系統要求

- Node.js 14.0 或更高版本
- FUTU OpenD 已安裝並運行
- 有效的 FUTU 帳號和期貨權限

## 安裝步驟

1. **克隆或下載項目**
   ```bash
   git clone <repository-url>
   cd MHI
   ```

2. **安裝依賴**
   ```bash
   npm install
   ```

3. **配置 FUTU OpenD**
   - 確保 FUTU OpenD 已啟動
   - 確認 WebSocket 服務在端口 33333 上運行
   - 記錄您的 WebSocket 密鑰

## 配置

在 `index.js` 文件中修改以下配置：

```javascript
const CONFIG = {
    host: '127.0.0.1',                    // FUTU OpenD 主機地址
    port: 33333,                          // WebSocket 端口
    enableSSL: false,                     // 是否啟用 SSL
    websocketKey: '3e8229abd3ccdfdc',     // 您的 WebSocket 密鑰
    futuAccount: '********',              // 您的牛牛號
    password: 'Leo@0609'                  // 您的登錄密碼
};
```

**重要**: 程式已更新為使用真實的 FUTU API 連接，包含正確的登錄認證。

## 🚀 快速開始

### 網頁版（推薦）

1. **啟動 FUTU OpenD**
   - 打開 FUTU OpenD 應用程式
   - 使用您的帳號登錄
   - 確保 WebSocket 服務已啟用

2. **啟動網頁服務器**
   ```bash
   npm start
   ```

3. **打開瀏覽器**
   訪問：http://localhost:3000

### 控制台版

1. **啟動 FUTU OpenD**（同上）

2. **運行控制台程式**
   ```bash
   npm run console  # 互動式選單
   npm run tracker  # 直接運行追蹤器
   ```

3. **查看實時數據**
   程式將顯示類似以下的輸出：
   ```
   🚀 啟動恆生指數期貨實時價格追蹤器...
   📊 目標合約: MHI2501
   🔑 使用帳號: ********
   🔗 已連接到 FUTU OpenD
   📈 已訂閱 MHI2501 實時行情
   ✅ 系統已啟動，開始接收實時數據...

   📊 ===== 恆生指數期貨快照 =====
   🕐 時間: 2025/1/26 下午2:30:15
   📈 合約: MHI2501
   💰 最新價: 19850
   📊 開盤價: 19800
   📈 最高價: 19900
   📉 最低價: 19750
   📊 成交量: 12500
   💹 漲跌額: +50
   📊 漲跌幅: +0.25%
   =====================================

   🕐 2025/1/26 下午2:30:16 | 💰 19855 📈 (+5) | 📊 成交量: 12510
   🕐 2025/1/26 下午2:30:17 | 💰 19860 📈 (+5) | 📊 成交量: 12520
   ```

## 期貨合約代碼

程式預設追蹤 Mini HSI 期貨。您可能需要根據當前月份調整合約代碼：

- `MHI2501` - 2025年1月到期
- `MHI2502` - 2025年2月到期
- `MHI2503` - 2025年3月到期

在 `index.js` 中修改 `HSI_FUTURES.code` 來更改追蹤的合約。

## 權限要求

- 您的 FUTU 帳號需要有期貨 LV1 權限
- 確保帳號可以訂閱和讀取期貨數據

## 故障排除

### 連接問題
- 確認 FUTU OpenD 正在運行
- 檢查 WebSocket 端口是否正確（預設 33333）
- 驗證 WebSocket 密鑰是否正確

### 權限問題
- 確認您的帳號有期貨權限
- 檢查是否已登錄 FUTU OpenD

### 數據問題
- 確認期貨合約代碼是否正確
- 檢查市場是否開放交易

## 注意事項

- 此程式僅用於教育和研究目的
- 實際交易前請確保充分了解風險
- 遵守相關法規和交易所規則
- 建議在模擬環境中先行測試

## 技術支援

如遇到問題，請檢查：
1. FUTU OpenD 日誌
2. 網絡連接狀態
3. 帳號權限設置
4. 期貨合約是否有效

## 授權

此項目僅供學習和研究使用。
