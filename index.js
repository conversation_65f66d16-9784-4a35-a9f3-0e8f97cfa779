const WebSocket = require('ws');

// 配置信息
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',  // 您的 WebSocket 密鑰
    futuAccount: '********',           // 您的牛牛號
    password: 'leotam060690'           // 您的登錄密碼
};

// 恆生指數期貨代碼 (Mini HSI)
const HSI_FUTURES = {
    market: 1,  // 香港市場
    code: 'MHI2501'  // Mini HSI 期貨代碼，需要根據實際月份調整
};

class HSIFuturesTracker {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.lastPrice = null;
        this.priceHistory = [];
        this.heartbeatInterval = null;
    }

    async start() {
        try {
            console.log('🚀 啟動恆生指數期貨實時價格追蹤器...');
            console.log(`📊 目標合約: ${HSI_FUTURES.code}`);
            console.log(`🔑 使用帳號: ${CONFIG.futuAccount}`);
            console.log(`🔗 連接地址: ws://${CONFIG.host}:${CONFIG.port}`);

            // 設置事件監聽器
            this.setupEventListeners();

            // 連接到 FUTU OpenD
            await this.connect();

        } catch (error) {
            console.error('❌ 啟動失敗:', error.message);
            process.exit(1);
        }
    }

    setupEventListeners() {
        // 處理程序退出
        process.on('SIGINT', () => {
            console.log('\n🛑 正在關閉程序...');
            this.cleanup();
            process.exit(0);
        });
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                const protocol = CONFIG.enableSSL ? 'wss' : 'ws';
                const url = `${protocol}://${CONFIG.host}:${CONFIG.port}`;

                console.log(`🔗 正在連接到 ${url}...`);

                this.ws = new WebSocket(url);

                this.ws.on('open', () => {
                    console.log('✅ WebSocket 連接已建立');
                    this.isConnected = true;

                    // 開始心跳
                    this.startHeartbeat();

                    // 模擬接收數據
                    this.simulateData();

                    resolve();
                });

                this.ws.on('message', (data) => {
                    this.handleMessage(data);
                });

                this.ws.on('close', (code, reason) => {
                    console.log(`❌ WebSocket 連接已關閉 - 代碼: ${code}, 原因: ${reason}`);
                    this.isConnected = false;
                    this.stopHeartbeat();
                });

                this.ws.on('error', (error) => {
                    console.error('🚨 WebSocket 錯誤:', error.message);

                    if (error.code === 'ECONNREFUSED') {
                        console.log('\n💡 故障排除建議:');
                        console.log('1. 確認 FUTU OpenD 已啟動');
                        console.log('2. 檢查 WebSocket 端口是否為 33333');
                        console.log('3. 確認防火牆設置');
                        console.log('4. 確認 WebSocket 密鑰是否正確');
                    }

                    reject(error);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    handleMessage(data) {
        try {
            console.log('📨 收到服務器數據:', data.toString().substring(0, 100) + '...');
            // 這裡可以添加實際的協議解析邏輯
        } catch (error) {
            console.error('❌ 處理消息失敗:', error);
        }
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                // 發送心跳包
                const heartbeat = JSON.stringify({ type: 'ping', timestamp: Date.now() });
                this.ws.send(heartbeat);
                console.log('💓 發送心跳包');
            }
        }, 30000); // 每30秒發送一次心跳
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    simulateData() {
        // 模擬實時數據更新
        console.log('✅ 系統已啟動，開始模擬實時數據...');
        console.log('📝 注意: 這是模擬數據，實際使用需要正確的 FUTU API 協議實現');

        let basePrice = 19850;
        let volume = 12500;

        setInterval(() => {
            // 模擬價格波動
            const change = (Math.random() - 0.5) * 20; // ±10點波動
            basePrice += change;
            volume += Math.floor(Math.random() * 100);

            this.displayRealTimeQuote({
                curPrice: basePrice,
                volume: volume,
                changeVal: change
            });
        }, 1000); // 每秒更新一次
    }

    displayRealTimeQuote(quote) {
        const currentTime = new Date().toLocaleString('zh-TW');
        const price = quote.curPrice;

        // 計算價格變化
        let priceChange = '';
        if (this.lastPrice && price) {
            const change = price - this.lastPrice;
            if (change > 0) {
                priceChange = `📈 (+${change.toFixed(2)})`;
            } else if (change < 0) {
                priceChange = `📉 (${change.toFixed(2)})`;
            } else {
                priceChange = `➡️ (0.00)`;
            }
        }

        console.log(`🕐 ${currentTime} | 💰 ${price.toFixed(2)} ${priceChange} | 📊 成交量: ${quote.volume || 'N/A'}`);

        // 更新價格歷史
        if (price) {
            this.lastPrice = price;
            this.priceHistory.push({
                time: new Date(),
                price: price,
                volume: quote.volume
            });

            // 保持最近100筆記錄
            if (this.priceHistory.length > 100) {
                this.priceHistory.shift();
            }
        }
    }

    cleanup() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
    }
}

// 啟動程序
const tracker = new HSIFuturesTracker();
tracker.start().catch(error => {
    console.error('❌ 程序啟動失敗:', error);
    process.exit(1);
});
