// 全局變量
let socket;
let priceChart;
let priceData = [];
let autoScroll = true;
let dayHigh = null;
let dayLow = null;

// 初始化應用程序
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    initializeChart();
    hideLoadingOverlay();
});

// 初始化 Socket.IO 連接
function initializeSocket() {
    socket = io();
    
    // 連接事件
    socket.on('connect', function() {
        console.log('✅ 已連接到服務器');
        updateConnectionStatus(true);
    });
    
    socket.on('disconnect', function() {
        console.log('❌ 與服務器斷開連接');
        updateConnectionStatus(false);
    });
    
    // 狀態更新
    socket.on('status', function(data) {
        updateStatus(data);
    });
    
    // 價格更新
    socket.on('price_update', function(data) {
        updatePrice(data);
        addToDataStream(data);
        updateChart(data);
        updateStatistics(data);
    });
    
    // 歷史數據
    socket.on('history', function(data) {
        console.log('📊 收到歷史數據:', data.length, '筆記錄');
        priceData = data;
        if (data.length > 0) {
            initializeStatistics(data);
            updateChartWithHistory(data);
        }
    });
}

// 初始化圖表
function initializeChart() {
    const ctx = document.getElementById('price-chart').getContext('2d');
    
    priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '價格',
                data: [],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 0,
                pointHoverRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#3498db',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// 更新連接狀態
function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connection-status');
    if (connected) {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 已連接';
        statusElement.className = 'status-value connected';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> 未連接';
        statusElement.className = 'status-value disconnected';
    }
}

// 更新狀態信息
function updateStatus(data) {
    document.getElementById('contract-code').textContent = data.contract;
    document.getElementById('account-id').textContent = data.account;
    updateConnectionStatus(data.connected);

    // 更新數據類型顯示
    if (data.dataType) {
        const dataTypeElement = document.getElementById('data-type');
        if (dataTypeElement) {
            if (data.dataType === 'real') {
                dataTypeElement.innerHTML = '<i class="fas fa-database"></i> 真實數據';
                dataTypeElement.className = 'status-value connected';
            } else {
                dataTypeElement.innerHTML = '<i class="fas fa-flask"></i> 模擬數據';
                dataTypeElement.className = 'status-value disconnected';
            }
        }
    }
}

// 更新價格顯示
function updatePrice(data) {
    const currentPriceElement = document.getElementById('current-price');
    const priceChangeElement = document.getElementById('price-change');
    const priceChangePercentElement = document.getElementById('price-change-percent');
    const lastUpdateElement = document.getElementById('last-update');
    
    // 更新當前價格
    currentPriceElement.textContent = data.price.toFixed(2);
    
    // 更新價格變化
    const change = data.change;
    const changePercent = data.changePercent;
    
    let changeClass = 'change-neutral';
    let changeIcon = 'fas fa-minus';
    
    if (change > 0) {
        changeClass = 'change-positive';
        changeIcon = 'fas fa-arrow-up';
    } else if (change < 0) {
        changeClass = 'change-negative';
        changeIcon = 'fas fa-arrow-down';
    }
    
    priceChangeElement.className = changeClass;
    priceChangeElement.innerHTML = `<i class="${changeIcon}"></i> ${change.toFixed(2)}`;
    priceChangePercentElement.textContent = `(${changePercent.toFixed(2)}%)`;
    priceChangePercentElement.className = `change-percent ${changeClass}`;
    
    // 更新最後更新時間
    const updateTime = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    lastUpdateElement.textContent = updateTime;
}

// 添加到數據流
function addToDataStream(data) {
    const streamElement = document.getElementById('data-stream');
    const time = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    
    let changeClass = '';
    let changeIcon = '➡️';
    
    if (data.change > 0) {
        changeClass = 'positive';
        changeIcon = '📈';
    } else if (data.change < 0) {
        changeClass = 'negative';
        changeIcon = '📉';
    }
    
    const streamItem = document.createElement('div');
    streamItem.className = `stream-item ${changeClass}`;
    streamItem.innerHTML = `
        <strong>${time}</strong> | 
        💰 ${data.price.toFixed(2)} ${changeIcon} (${data.change.toFixed(2)}) | 
        📊 成交量: ${data.volume.toLocaleString()}
    `;
    
    streamElement.appendChild(streamItem);
    
    // 保持最多100條記錄
    while (streamElement.children.length > 100) {
        streamElement.removeChild(streamElement.firstChild);
    }
    
    // 自動滾動到底部
    if (autoScroll) {
        streamElement.scrollTop = streamElement.scrollHeight;
    }
}

// 更新圖表
function updateChart(data) {
    const time = new Date(data.timestamp).toLocaleTimeString('zh-TW');
    
    priceChart.data.labels.push(time);
    priceChart.data.datasets[0].data.push(data.price);
    
    // 保持最多50個數據點
    if (priceChart.data.labels.length > 50) {
        priceChart.data.labels.shift();
        priceChart.data.datasets[0].data.shift();
    }
    
    priceChart.update('none');
}

// 使用歷史數據更新圖表
function updateChartWithHistory(historyData) {
    const labels = [];
    const prices = [];
    
    // 取最近50筆數據
    const recentData = historyData.slice(-50);
    
    recentData.forEach(item => {
        const time = new Date(item.timestamp).toLocaleTimeString('zh-TW');
        labels.push(time);
        prices.push(item.price);
    });
    
    priceChart.data.labels = labels;
    priceChart.data.datasets[0].data = prices;
    priceChart.update();
}

// 更新統計信息
function updateStatistics(data) {
    // 更新成交量
    document.getElementById('volume').textContent = data.volume.toLocaleString();
    
    // 更新最高價和最低價
    if (dayHigh === null || data.price > dayHigh) {
        dayHigh = data.price;
        document.getElementById('day-high').textContent = dayHigh.toFixed(2);
    }
    
    if (dayLow === null || data.price < dayLow) {
        dayLow = data.price;
        document.getElementById('day-low').textContent = dayLow.toFixed(2);
    }
}

// 初始化統計信息
function initializeStatistics(historyData) {
    if (historyData.length === 0) return;
    
    const prices = historyData.map(item => item.price);
    dayHigh = Math.max(...prices);
    dayLow = Math.min(...prices);
    
    document.getElementById('day-high').textContent = dayHigh.toFixed(2);
    document.getElementById('day-low').textContent = dayLow.toFixed(2);
    
    // 顯示最新的成交量
    const latestData = historyData[historyData.length - 1];
    document.getElementById('volume').textContent = latestData.volume.toLocaleString();
}

// 控制函數
function reconnect() {
    socket.emit('reconnect_request');
    showLoadingOverlay();
    setTimeout(hideLoadingOverlay, 3000);
}

function clearChart() {
    priceChart.data.labels = [];
    priceChart.data.datasets[0].data = [];
    priceChart.update();
}

function clearStream() {
    document.getElementById('data-stream').innerHTML = '';
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const scrollText = document.getElementById('scroll-text');
    scrollText.textContent = autoScroll ? '停止滾動' : '開始滾動';
}

function exportData() {
    if (priceData.length === 0) {
        alert('沒有數據可以導出');
        return;
    }
    
    const csvContent = "data:text/csv;charset=utf-8," 
        + "時間,價格,變化,變化百分比,成交量\n"
        + priceData.map(item => {
            const time = new Date(item.timestamp).toLocaleString('zh-TW');
            return `${time},${item.price},${item.change},${item.changePercent},${item.volume}`;
        }).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `HSI_futures_data_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showLoadingOverlay() {
    document.getElementById('loading-overlay').style.display = 'flex';
}

function hideLoadingOverlay() {
    document.getElementById('loading-overlay').style.display = 'none';
}
