syntax = "proto2";
package Qot_GetHistoryKL;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgethistorykl";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required int32 rehabType = 1; //Qot_Common.RehabType,复权类型
	required int32 klType = 2; //Qot_Common.KLType,K线类型
	required Qot_Common.Security security = 3; //股票市场以及股票代码
	required string beginTime = 4; //开始时间字符串
	required string endTime = 5; //结束时间字符串
	optional int32 maxAckKLNum = 6; //最多返回多少根K线，如果未指定表示不限制
	optional int64 needKLFieldsFlag = 7; //指定返回K线结构体特定某几项数据，KLFields枚举值或组合，如果未指定返回全部字段
}

message S2C
{
	required Qot_Common.Security security = 1;
	repeated Qot_Common.KLine klList = 2; //K线数据
	optional string nextKLTime = 3; //如请求不指定maxAckKLNum值，则不会返回该字段，该字段表示超过指定限制的下一K线时间字符串
	optional double nextKLTimestamp = 4; //时间戳，如请求不指定maxAckKLNum值，则不会返回该字段，该字段表示超过指定限制的下一K线时间戳
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
