/* 全局樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 標題區域 */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    color: #2c3e50;
    font-size: 2.2em;
    margin-bottom: 15px;
    text-align: center;
}

.header h1 i {
    color: #3498db;
    margin-right: 10px;
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-weight: 600;
    color: #7f8c8d;
}

.status-value {
    font-weight: 700;
    padding: 5px 12px;
    border-radius: 20px;
    background: #ecf0f1;
}

.status-value.connected {
    background: #d5f4e6;
    color: #27ae60;
}

.status-value.disconnected {
    background: #fadbd8;
    color: #e74c3c;
}

.contract-display {
    min-width: 200px;
    font-family: 'Courier New', monospace;
    font-weight: 700;
}

/* 主要內容區域 */
.main-content {
    display: grid;
    gap: 25px;
}

/* 價格卡片 */
.price-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.price-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.price-header h2 {
    color: #2c3e50;
    font-size: 1.5em;
}

.price-header h2 i {
    color: #f39c12;
    margin-right: 8px;
}

.last-update {
    color: #7f8c8d;
    font-size: 0.9em;
}

.price-display {
    text-align: center;
}

.current-price {
    font-size: 3.5em;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.currency {
    font-size: 0.6em;
    color: #7f8c8d;
    margin-left: 10px;
}

.price-change {
    font-size: 1.3em;
    font-weight: 600;
}

.change-positive {
    color: #27ae60;
}

.change-negative {
    color: #e74c3c;
}

.change-neutral {
    color: #7f8c8d;
}

.change-percent {
    margin-left: 10px;
    font-size: 0.9em;
}

/* 統計網格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    color: white;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-content {
    flex: 1;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.stat-value {
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 700;
}

/* 圖表容器 */
.chart-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    color: #2c3e50;
    font-size: 1.3em;
}

.chart-header h3 i {
    color: #9b59b6;
    margin-right: 8px;
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

/* 數據流 */
.data-stream {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.stream-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stream-header h3 {
    color: #2c3e50;
    font-size: 1.3em;
}

.stream-header h3 i {
    color: #e67e22;
    margin-right: 8px;
}

.stream-content {
    height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

.stream-item {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 5px;
    background: white;
    border-left: 4px solid #3498db;
}

.stream-item.positive {
    border-left-color: #27ae60;
}

.stream-item.negative {
    border-left-color: #e74c3c;
}

/* 按鈕樣式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8em;
}

.chart-controls, .stream-controls {
    display: flex;
    gap: 10px;
}

/* 控制面板 */
.control-panel {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 25px;
}

/* 載入覆蓋層 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 15px;
    }
    
    .current-price {
        font-size: 2.5em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-header, .stream-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .control-panel {
        flex-direction: column;
        align-items: center;
    }
}
