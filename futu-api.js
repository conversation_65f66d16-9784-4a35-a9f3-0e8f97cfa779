const WebSocket = require('ws');
const protoRoot = require('./proto.js');
const protobuf = require('protobufjs');
const crypto = require('crypto');
const long = require('long');

protobuf.util.Long = long;
protobuf.configure();

// 命令ID定義
const ftCmdID = {
    InitConnect: { cmd: 1001, name: 'InitConnect', description: '初始化連接' },
    QotSub: { cmd: 3001, name: 'Qot_Sub', description: '訂閱或者反訂閱' },
    QotGetSecuritySnapshot: { cmd: 3203, name: 'Qot_GetSecuritySnapshot', description: '獲取股票快照' },
    QotUpdateBasicQot: { cmd: 3005, name: 'Qot_UpdateBasicQot', description: '推送基本行情' },
};

class FutuAPI {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        this.onQuotePush = null;
        this.requestId = 1;
        this.pendingRequests = new Map();
    }

    async connect(host, port, enableSSL = false, key = null, userID = null, password = null) {
        return new Promise((resolve, reject) => {
            try {
                const protocol = enableSSL ? 'wss' : 'ws';
                const url = `${protocol}://${host}:${port}`;

                console.log(`🔗 正在連接到 ${url}...`);
                if (userID) {
                    console.log(`👤 使用帳號登錄: ${userID}`);
                }

                this.ws = new WebSocket(url);

                this.ws.on('open', async () => {
                    console.log('🔗 WebSocket 連接已建立');

                    try {
                        // 初始化連接，包含登錄信息
                        await this.initConnect(key, userID, password);
                        this.isConnected = true;

                        if (this.onConnect) {
                            this.onConnect();
                        }

                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                });

                this.ws.on('message', (data) => {
                    this.handleMessage(data);
                });

                this.ws.on('close', () => {
                    console.log('❌ WebSocket 連接已關閉');
                    this.isConnected = false;
                    
                    if (this.onDisconnect) {
                        this.onDisconnect();
                    }
                });

                this.ws.on('error', (error) => {
                    console.error('🚨 WebSocket 錯誤:', error);
                    
                    if (this.onError) {
                        this.onError(error);
                    }
                    
                    reject(error);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    async initConnect(key, userID, password) {
        const req = {
            c2s: {
                clientVer: 308,
                clientID: 'FutuAPI_JS',
                recvNotify: true,
                packetEncAlgo: 0,
                pushProtoFmt: 1
            }
        };

        if (userID && password) {
            req.c2s.loginUserID = parseInt(userID);
            req.c2s.loginPwdMD5 = crypto.createHash('md5').update(password).digest('hex');
        } else if (key) {
            req.c2s.loginUserID = 0;
            req.c2s.loginPwdMD5 = crypto.createHash('md5').update(key).digest('hex');
        }

        return this.sendRequest(ftCmdID.InitConnect.cmd, req);
    }

    async subscribe(request) {
        return this.sendRequest(ftCmdID.QotSub.cmd, request);
    }

    async getSecuritySnapshot(request) {
        return this.sendRequest(ftCmdID.QotGetSecuritySnapshot.cmd, request);
    }

    sendRequest(cmdId, data) {
        return new Promise((resolve, reject) => {
            try {
                const requestId = this.requestId++;
                
                // 查找對應的協議
                const cmdObj = this.findCmdObj(cmdId);
                if (!cmdObj) {
                    reject(new Error(`未知的命令ID: ${cmdId}`));
                    return;
                }

                // 編碼請求
                const reqType = protoRoot.lookupType(cmdObj.name + '.Request');
                const message = reqType.create(data);
                const buffer = reqType.encode(message).finish();

                // 創建包頭
                const header = this.createPacketHeader(cmdId, requestId, buffer.length);
                
                // 發送數據
                const packet = Buffer.concat([header, buffer]);
                this.ws.send(packet);

                // 保存請求回調
                this.pendingRequests.set(requestId, { resolve, reject, cmdId });

                // 設置超時
                setTimeout(() => {
                    if (this.pendingRequests.has(requestId)) {
                        this.pendingRequests.delete(requestId);
                        reject(new Error('請求超時'));
                    }
                }, 10000);

            } catch (error) {
                reject(error);
            }
        });
    }

    handleMessage(data) {
        try {
            // 解析包頭
            const header = this.parsePacketHeader(data);
            
            if (header.bodyLen > 0) {
                const bodyData = data.slice(20, 20 + header.bodyLen);
                
                // 查找對應的協議
                const cmdObj = this.findCmdObj(header.cmdId);
                if (!cmdObj) {
                    console.warn(`未知的命令ID: ${header.cmdId}`);
                    return;
                }

                // 解碼響應
                const resType = protoRoot.lookupType(cmdObj.name + '.Response');
                const response = resType.decode(bodyData);

                // 處理響應
                if (this.pendingRequests.has(header.serialNo)) {
                    // 這是對請求的響應
                    const request = this.pendingRequests.get(header.serialNo);
                    this.pendingRequests.delete(header.serialNo);
                    
                    if (response.retType === 0) {
                        request.resolve(response);
                    } else {
                        request.reject(new Error(response.retMsg || '請求失敗'));
                    }
                } else {
                    // 這是推送數據
                    this.handlePushData(header.cmdId, response);
                }
            }

        } catch (error) {
            console.error('❌ 處理消息失敗:', error);
        }
    }

    handlePushData(cmdId, data) {
        // 處理實時行情推送
        if (cmdId === ftCmdID.QotUpdateBasicQot.cmd) {
            if (this.onQuotePush) {
                this.onQuotePush(data);
            }
        }
    }

    createPacketHeader(cmdId, serialNo, bodyLen) {
        const buffer = Buffer.alloc(20);
        
        buffer.writeUInt8(0x46, 0);  // 'F'
        buffer.writeUInt8(0x54, 1);  // 'T'
        buffer.writeUInt32LE(cmdId, 2);
        buffer.writeUInt32LE(serialNo, 6);
        buffer.writeUInt32LE(bodyLen, 10);
        buffer.writeUInt8(0, 14);    // sha1
        buffer.writeUInt8(0, 15);    // reserved
        buffer.writeUInt32LE(0, 16); // reserved
        
        return buffer;
    }

    parsePacketHeader(data) {
        return {
            cmdId: data.readUInt32LE(2),
            serialNo: data.readUInt32LE(6),
            bodyLen: data.readUInt32LE(10)
        };
    }

    findCmdObj(cmdId) {
        for (const key in ftCmdID) {
            if (ftCmdID[key].cmd === cmdId) {
                return ftCmdID[key];
            }
        }
        return null;
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
    }
}

module.exports = FutuAPI;
