@echo off
title 恆生指數期貨實時追蹤器
color 0A

echo.
echo ========================================
echo    恆生指數期貨實時追蹤器 - 獨立版
echo ========================================
echo.
echo � 請選擇小型恆生指數期貨合約:
echo.
echo 1. MHI2501 - 2025年1月到期
echo 2. MHI2502 - 2025年2月到期
echo 3. MHI2503 - 2025年3月到期
echo 4. MHI2504 - 2025年4月到期
echo 5. MHI2505 - 2025年5月到期
echo 6. MHI2506 - 2025年6月到期
echo.
set /p choice="請輸入選項 (1-6，預設為1): "

if "%choice%"=="" set choice=1
if "%choice%"=="1" set contract=MHI2501&set month=2025年1月
if "%choice%"=="2" set contract=MHI2502&set month=2025年2月
if "%choice%"=="3" set contract=MHI2503&set month=2025年3月
if "%choice%"=="4" set contract=MHI2504&set month=2025年4月
if "%choice%"=="5" set contract=MHI2505&set month=2025年5月
if "%choice%"=="6" set contract=MHI2506&set month=2025年6月

if not defined contract (
    echo ❌ 無效選項，使用預設合約 MHI2501
    set contract=MHI2501
    set month=2025年1月
)

echo.
echo �🚀 正在啟動程序...
echo 📊 目標合約: %contract% (%month%到期)
echo 🔑 帳號: 22188140
echo.

echo 🔍 正在檢測 FUTU OpenD 連接...
echo.

start "" "mhi-futures-tracker.exe" --contract=%contract%

echo ✅ 程序已啟動！
echo 📱 瀏覽器將自動打開網頁界面
echo.
echo 💡 使用說明:
echo    - 程序會自動檢測 FUTU OpenD 可視化模式
echo    - 如果連接成功，將顯示真實數據
echo    - 否則顯示模擬數據供演示
echo    - 關閉此窗口不會影響程序運行
echo.
echo � 如果無法連接 FUTU OpenD:
echo    1. 確認 FUTU OpenD 已啟動並登錄
echo    2. 檢查 API 設置中的 WebSocket 配置
echo    3. 查看程序控制台的詳細說明
echo.
echo �🛑 要完全退出程序，請在程序窗口按 Ctrl+C
echo.

timeout /t 8 /nobreak >nul
exit
