{"name": "futu-api", "version": "9.3.5308", "license": "ISC", "description": "Futu Websocket API for Node.js", "keywords": ["futu", "api", "futu-api", "futuopend"], "main": "main.js", "scripts": {"build:pb": "npx pbjs -t json-module -w commonjs -o proto.js proto/*.proto", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"protobufjs": "^6.8.8"}, "dependencies": {"long": "^4.0.0", "protobufjs": "^6.8.8", "bytebuffer": "^5.0.1"}}