恆生指數期貨實時追蹤器 - 獨立版
=====================================

📋 文件說明
-----------
mhi-futures-tracker.exe  - 主程序文件
啟動程序.bat            - 快速啟動腳本 (支援合約選擇)
public/                 - 網頁界面文件
使用說明.txt            - 本文件

🚀 啟動方法
-----------
方法1: 雙擊 "啟動程序.bat" (推薦)
       - 可選擇不同的期貨合約
       - 自動檢測連接狀態
方法2: 雙擊 "mhi-futures-tracker.exe"
       - 使用預設合約 MHI2501

📊 功能特色
-----------
✅ 獨立運行，無需安裝 Node.js
✅ 自動打開瀏覽器界面
✅ 智能檢測 FUTU OpenD 可視化模式
✅ 支持真實數據和模擬數據自動切換
✅ 🆕 動態合約選擇 - 即時切換不同期貨合約
✅ 🆕 自動搜尋 FUTU 內現有合約
✅ 🆕 智能推薦最佳流動性合約
✅ 現代化網頁界面
✅ 實時圖表和統計
✅ 數據導出功能
✅ 內建連接診斷工具

🔧 配置信息
-----------
帳號: 22188140
密碼: Leo@0609
合約: 可選擇 (MHI2501-MHI2506)
端口: 自動選擇 (3000-3003, 8080-8081)

📊 可選合約
-----------
1. MHI2501 - 2025年1月到期 (推薦，流動性最高)
2. MHI2502 - 2025年2月到期
3. MHI2503 - 2025年3月到期
4. MHI2504 - 2025年4月到期
5. MHI2505 - 2025年5月到期
6. MHI2506 - 2025年6月到期

💡 合約選擇建議:
- 日內交易: 選擇 MHI2501 (最高流動性)
- 短期投資: 選擇 MHI2501 或 MHI2502
- 中長期: 選擇 MHI2503-MHI2506

📱 使用說明
-----------
1. 運行程序後會自動打開瀏覽器
2. 如果 FUTU OpenD 正在運行，將顯示真實數據
3. 否則顯示模擬數據供演示使用
4. 🆕 點擊狀態欄中的合約區域可選擇不同合約
5. 🆕 程序會自動搜尋 FUTU 內現有的期貨合約
6. 🆕 選擇合約後立即切換，無需重啟程序
7. 網頁界面提供完整的功能操作

🎯 合約選擇功能
--------------
• 點擊合約顯示區域打開選擇器
• 搜尋功能：可按合約代碼或到期時間搜尋
• 智能標識：顯示當前、推薦、活躍合約
• 即時切換：選擇後立即生效
• 自動推薦：優先顯示流動性最高的合約

🔗 FUTU OpenD 可視化模式連接
-----------------------------
要獲取真實數據，請確保:
✅ FUTU OpenD 已安裝並啟動
✅ 使用帳號 22188140 登錄
✅ 在 FUTU OpenD 中啟用 API 服務
✅ 啟用 WebSocket 服務 (端口 33333)
✅ WebSocket 密鑰: 3e8229abd3ccdfdc
✅ 允許本地連接 (127.0.0.1)

💡 可視化模式特別說明:
- 程序會自動檢測並適配可視化模式
- 支持多種連接方式自動切換
- 內建智能診斷和重試機制

🛑 退出程序
-----------
- 在程序控制台窗口按 Ctrl+C
- 或關閉所有相關窗口

⚠️  注意事項
------------
- 程序需要網絡連接
- 防火牆可能需要允許程序運行
- 首次運行可能需要管理員權限

📞 技術支援
-----------
如遇問題，請檢查:
1. 防火牆設置
2. FUTU OpenD 配置
3. 網絡連接狀態

版本: 1.0.0
更新日期: 2025-01-27
