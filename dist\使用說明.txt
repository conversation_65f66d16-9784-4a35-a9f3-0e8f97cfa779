恆生指數期貨實時追蹤器 - 獨立版
=====================================

📋 文件說明
-----------
mhi-futures-tracker.exe  - 主程序文件
啟動程序.bat            - 快速啟動腳本
public/                 - 網頁界面文件
使用說明.txt            - 本文件

🚀 啟動方法
-----------
方法1: 雙擊 "啟動程序.bat"
方法2: 雙擊 "mhi-futures-tracker.exe"

📊 功能特色
-----------
✅ 獨立運行，無需安裝 Node.js
✅ 自動打開瀏覽器界面
✅ 支持真實數據和模擬數據
✅ 現代化網頁界面
✅ 實時圖表和統計
✅ 數據導出功能

🔧 配置信息
-----------
帳號: 22188140
密碼: Leo@0609
合約: MHI2501 (Mini HSI 2025年1月)
端口: 自動選擇 (3000-3003, 8080-8081)

📱 使用說明
-----------
1. 運行程序後會自動打開瀏覽器
2. 如果 FUTU OpenD 正在運行，將顯示真實數據
3. 否則顯示模擬數據供演示使用
4. 網頁界面提供完整的功能操作

🔗 FUTU OpenD 連接
------------------
要獲取真實數據，請確保:
✅ FUTU OpenD 已安裝並啟動
✅ 使用帳號 22188140 登錄
✅ WebSocket 服務已啟用 (端口 33333)
✅ WebSocket 密鑰: 3e8229abd3ccdfdc

🛑 退出程序
-----------
- 在程序控制台窗口按 Ctrl+C
- 或關閉所有相關窗口

⚠️  注意事項
------------
- 程序需要網絡連接
- 防火牆可能需要允許程序運行
- 首次運行可能需要管理員權限

📞 技術支援
-----------
如遇問題，請檢查:
1. 防火牆設置
2. FUTU OpenD 配置
3. 網絡連接狀態

版本: 1.0.0
更新日期: 2025-01-27
