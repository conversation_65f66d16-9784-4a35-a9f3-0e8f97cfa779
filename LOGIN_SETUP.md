# FUTU API 登錄設置指南

## 🔐 更新的登錄信息

程式已更新為使用您提供的正確登錄信息：

- **牛牛號**: 22188140
- **登錄密碼**: leotam060690
- **WebSocket 密鑰**: 3e8229abd3ccdfdc

## 🚀 測試連接

### 1. 測試基本連接
```bash
npm run test-connection
```

### 2. 測試登錄認證
```bash
npm run test-login
```

### 3. 啟動網頁版
```bash
npm start
```
然後訪問：http://localhost:3000

## 🔧 FUTU OpenD 設置要求

### 必要設置
1. **啟動 FUTU OpenD**
   - 確保 FUTU OpenD 應用程式正在運行
   - 使用您的帳號 22188140 登錄

2. **WebSocket 配置**
   - 確認 WebSocket 服務已啟用
   - 端口設置為 33333
   - WebSocket 密鑰設置為：3e8229abd3ccdfdc

3. **API 權限**
   - 確認帳號有 OpenAPI 使用權限
   - 確認有期貨 LV1 權限
   - 檢查 API 調用限制

## 📊 數據類型說明

程式現在支持兩種數據模式：

### 🟢 真實數據模式
- 當成功連接到 FUTU OpenD 時啟用
- 顯示真實的期貨價格和成交量
- 狀態欄顯示 "真實數據"

### 🟡 模擬數據模式
- 當無法連接到 FUTU OpenD 時自動啟用
- 顯示模擬的價格波動
- 狀態欄顯示 "模擬數據"

## 🔍 故障排除

### 連接問題
如果無法連接，請檢查：

1. **FUTU OpenD 狀態**
   ```
   ✅ FUTU OpenD 已啟動
   ✅ 已使用帳號 22188140 登錄
   ✅ WebSocket 服務已啟用
   ```

2. **網絡設置**
   ```
   ✅ 端口 33333 未被阻擋
   ✅ 防火牆允許連接
   ✅ 本地網絡正常
   ```

3. **API 權限**
   ```
   ✅ 帳號有 OpenAPI 權限
   ✅ 帳號有期貨權限
   ✅ API 調用額度充足
   ```

### 認證問題
如果認證失敗，請檢查：

1. **登錄信息**
   - 帳號：22188140
   - 密碼：leotam060690
   - 確認密碼正確無誤

2. **WebSocket 密鑰**
   - 密鑰：3e8229abd3ccdfdc
   - 確認在 FUTU OpenD 中設置正確

3. **帳號狀態**
   - 帳號未被鎖定
   - 帳號有效且活躍
   - 沒有安全限制

### 數據問題
如果沒有數據更新：

1. **市場狀態**
   - 確認期貨市場開放時間
   - 檢查合約代碼是否正確（MHI2501）
   - 確認合約未到期

2. **訂閱設置**
   - 確認訂閱請求成功
   - 檢查數據推送設置
   - 驗證權限級別

## 📝 日誌分析

### 成功連接的日誌
```
🔗 正在連接到 FUTU OpenD: 127.0.0.1:33333
👤 使用帳號: 22188140
✅ WebSocket 連接已建立
✅ FUTU API 連接成功
📈 已訂閱 MHI2501 實時行情
📊 收到初始快照數據
```

### 失敗連接的日誌
```
🚨 FUTU API 錯誤: Connection refused
❌ 連接失敗: ECONNREFUSED
🔄 回退到模擬數據模式
```

### 認證失敗的日誌
```
🚨 FUTU API 錯誤: Authentication failed
❌ 登錄驗證失敗
🔄 回退到模擬數據模式
```

## 🎯 下一步

1. **確認 FUTU OpenD 設置**
   - 按照上述要求檢查所有設置
   - 確保帳號和密碼正確

2. **測試連接**
   ```bash
   npm run test-login
   ```

3. **啟動網頁版**
   ```bash
   npm start
   ```

4. **監控狀態**
   - 查看網頁上的連接狀態
   - 檢查數據類型顯示
   - 觀察實時數據更新

## 📞 技術支援

如果問題持續存在：

1. **檢查 FUTU OpenD 日誌**
   - 查看 OpenD 的錯誤信息
   - 確認 API 調用記錄

2. **網絡診斷**
   - 測試端口連通性
   - 檢查防火牆設置

3. **聯繫 FUTU 支援**
   - 確認帳號 API 權限
   - 驗證 WebSocket 配置

## 🔮 預期結果

成功設置後，您應該看到：

- ✅ 網頁顯示 "已連接" 狀態
- ✅ 數據類型顯示 "真實數據"
- ✅ 實時價格每秒更新
- ✅ 圖表顯示真實的價格走勢
- ✅ 控制台顯示成功連接日誌

這將為您提供真實的恆生指數期貨實時數據，而不是模擬數據。
