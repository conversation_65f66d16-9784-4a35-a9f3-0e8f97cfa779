#!/usr/bin/env node

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { spawn } = require('child_process');
const FutuVisualConnector = require('./futu-visual-connector');
const FutuContractSearcher = require('./futu-contract-searcher');

// 配置信息
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',
    futuAccount: '********',
    password: '<PERSON>@0609'
};

// 小型恆生指數期貨合約選項
const MHI_CONTRACTS = {
    'MHImain': { code: 'MHImain', name: '小型恆生指數主連', market: 1 },
    'MHI2501': { code: 'MHI2501', name: '2025年1月到期', market: 1 },
    'MHI2502': { code: 'MHI2502', name: '2025年2月到期', market: 1 },
    'MHI2503': { code: 'MHI2503', name: '2025年3月到期', market: 1 },
    'MHI2504': { code: 'MHI2504', name: '2025年4月到期', market: 1 },
    'MHI2505': { code: 'MHI2505', name: '2025年5月到期', market: 1 },
    'MHI2506': { code: 'MHI2506', name: '2025年6月到期', market: 1 }
};

// 解析命令行參數獲取合約
function getSelectedContract() {
    const args = process.argv.slice(2);
    let selectedContract = 'MHImain'; // 預設使用主連合約

    // 查找 --contract 參數
    for (let i = 0; i < args.length; i++) {
        if (args[i].startsWith('--contract=')) {
            selectedContract = args[i].split('=')[1];
            break;
        }
    }

    // 驗證合約是否有效
    if (!MHI_CONTRACTS[selectedContract]) {
        console.log(`⚠️  無效的合約代碼: ${selectedContract}，使用預設合約 MHImain`);
        selectedContract = 'MHImain';
    }

    return MHI_CONTRACTS[selectedContract];
}

// 獲取選定的期貨合約
const HSI_FUTURES = getSelectedContract();

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 靜態文件服務
app.use(express.static(path.join(__dirname, 'public')));

// 主頁路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

class StandaloneFuturesTracker {
    constructor() {
        this.futuConnector = null;
        this.contractSearcher = null;
        this.isConnected = false;
        this.lastPrice = null;
        this.priceHistory = [];
        this.clients = new Set();
        this.dataInterval = null;
        this.useRealData = false;
        this.serverPort = 3000;
        this.connectionStatus = 'disconnected';
        this.availableContracts = [];
        this.currentContract = HSI_FUTURES;
    }

    async start() {
        console.log('🚀 啟動獨立版恆生指數期貨追蹤器...');
        console.log(`📊 目標合約: ${HSI_FUTURES.code} (${HSI_FUTURES.name})`);
        console.log(`🔑 使用帳號: ${CONFIG.futuAccount}`);
        console.log('');

        this.setupSocketIO();
        this.startWebServer();

        // 搜尋可用合約
        await this.searchAvailableContracts();

        this.connectToFutu();
    }

    setupSocketIO() {
        io.on('connection', (socket) => {
            console.log('👤 新用戶連接:', socket.id);
            this.clients.add(socket);

            // 發送當前狀態
            socket.emit('status', {
                connected: this.isConnected,
                contract: HSI_FUTURES.code,
                contractName: HSI_FUTURES.name,
                account: CONFIG.futuAccount,
                dataType: this.useRealData ? 'real' : 'simulated'
            });

            // 發送歷史數據
            if (this.priceHistory.length > 0) {
                socket.emit('history', this.priceHistory.slice(-50));
            }

            socket.on('disconnect', () => {
                console.log('👤 用戶斷開:', socket.id);
                this.clients.delete(socket);
            });

            socket.on('reconnect_request', () => {
                this.connectToFutu();
            });

            // 處理合約切換請求
            socket.on('change_contract', (contractCode) => {
                this.changeContract(contractCode);
            });

            // 處理獲取可用合約請求
            socket.on('get_contracts', () => {
                socket.emit('contracts_list', this.availableContracts);
            });
        });
    }

    startWebServer() {
        // 嘗試不同的端口
        const tryPorts = [3000, 3001, 3002, 3003, 8080, 8081];
        
        const tryNextPort = (portIndex = 0) => {
            if (portIndex >= tryPorts.length) {
                console.error('❌ 無法找到可用端口');
                process.exit(1);
                return;
            }

            const port = tryPorts[portIndex];
            
            server.listen(port, (err) => {
                if (err) {
                    console.log(`⚠️  端口 ${port} 被占用，嘗試下一個...`);
                    tryNextPort(portIndex + 1);
                } else {
                    this.serverPort = port;
                    console.log(`🌐 網頁服務器啟動在端口 ${port}`);
                    console.log(`📱 請在瀏覽器中訪問: http://localhost:${port}`);
                    
                    // 自動打開瀏覽器
                    this.openBrowser(`http://localhost:${port}`);
                }
            });

            server.on('error', (err) => {
                if (err.code === 'EADDRINUSE') {
                    console.log(`⚠️  端口 ${port} 被占用，嘗試下一個...`);
                    tryNextPort(portIndex + 1);
                }
            });
        };

        tryNextPort();
    }

    async searchAvailableContracts() {
        try {
            console.log('🔍 正在搜尋 FUTU 內現有的小型恆生指數期貨合約...');

            this.contractSearcher = new FutuContractSearcher(CONFIG);
            this.availableContracts = await this.contractSearcher.searchMHIContracts();

            if (this.availableContracts.length > 0) {
                console.log(`✅ 找到 ${this.availableContracts.length} 個可用合約`);

                // 設置推薦合約
                const recommended = this.contractSearcher.getRecommendedContract(this.availableContracts);
                if (recommended) {
                    this.availableContracts.forEach(contract => {
                        contract.isRecommended = contract.code === recommended.code;
                    });
                    console.log(`💡 推薦合約: ${recommended.code} (${recommended.name})`);
                }
            } else {
                console.log('⚠️  未找到可用合約，使用預設列表');
            }

        } catch (error) {
            console.log('❌ 搜尋合約失敗:', error.message);
            this.availableContracts = this.contractSearcher ?
                this.contractSearcher.getDefaultContracts() : [];
        }
    }

    changeContract(contractCode) {
        console.log(`🔄 正在切換到合約: ${contractCode}`);

        // 查找新合約
        const newContract = this.availableContracts.find(contract => contract.code === contractCode);

        if (newContract) {
            // 更新當前合約
            this.currentContract = {
                code: newContract.code,
                name: newContract.name,
                market: newContract.market || 1
            };

            console.log(`✅ 已切換到: ${this.currentContract.code} (${this.currentContract.name})`);

            // 清除歷史數據
            this.priceHistory = [];
            this.lastPrice = null;

            // 廣播狀態更新
            this.broadcastStatus();

            // 如果有真實連接，重新訂閱新合約
            if (this.isConnected && this.useRealData) {
                this.subscribeToNewContract();
            }

            // 廣播合約切換事件
            io.emit('contract_changed', {
                contract: this.currentContract,
                timestamp: new Date().toISOString()
            });

        } else {
            console.log(`❌ 無效的合約代碼: ${contractCode}`);

            // 通知客戶端錯誤
            io.emit('contract_change_error', {
                error: '無效的合約代碼',
                requestedContract: contractCode
            });
        }
    }

    async subscribeToNewContract() {
        try {
            console.log(`📡 正在訂閱新合約: ${this.currentContract.code}`);

            if (this.futuConnector && this.futuConnector.ws) {
                const subscribeRequest = {
                    cmd: 'subscribe',
                    data: {
                        symbol: this.currentContract.code,
                        market: this.currentContract.market,
                        subType: ['quote', 'ticker']
                    }
                };

                this.futuConnector.ws.send(JSON.stringify(subscribeRequest));
                console.log(`✅ 已訂閱合約: ${this.currentContract.code}`);
            }

        } catch (error) {
            console.log('❌ 訂閱新合約失敗:', error.message);
        }
    }

    openBrowser(url) {
        const start = (process.platform == 'darwin'? 'open': process.platform == 'win32'? 'start': 'xdg-open');
        
        setTimeout(() => {
            console.log(`🔗 正在打開瀏覽器: ${url}`);
            spawn(start, [url], { shell: true, detached: true, stdio: 'ignore' });
        }, 2000);
    }

    async connectToFutu() {
        console.log(`🔗 正在嘗試連接到 FUTU OpenD 可視化模式...`);
        console.log(`📍 目標地址: ${CONFIG.host}:${CONFIG.port}`);
        console.log(`👤 使用帳號: ${CONFIG.futuAccount}`);

        this.connectionStatus = 'connecting';
        this.broadcastStatus();

        try {
            // 創建可視化模式連接器
            this.futuConnector = new FutuVisualConnector(CONFIG);

            // 設置事件監聽器
            this.futuConnector.onConnect = () => {
                console.log('✅ FUTU OpenD 可視化模式連接成功！');
                this.isConnected = true;
                this.useRealData = true;
                this.connectionStatus = 'connected';
                this.broadcastStatus();

                // 開始接收真實數據
                this.startRealDataMode();
            };

            this.futuConnector.onDisconnect = () => {
                console.log('❌ FUTU OpenD 連接斷開');
                this.isConnected = false;
                this.useRealData = false;
                this.connectionStatus = 'disconnected';
                this.broadcastStatus();
                this.startDataSimulation();
            };

            this.futuConnector.onError = (error) => {
                console.log('🚨 FUTU OpenD 連接錯誤:', error.message);
                this.handleConnectionFailure();
            };

            this.futuConnector.onData = (data) => {
                this.handleRealData(data);
            };

            // 嘗試連接
            const success = await this.futuConnector.connect();

            if (!success) {
                this.handleConnectionFailure();
            }

        } catch (error) {
            console.log('❌ 連接過程中發生錯誤:', error.message);
            this.handleConnectionFailure();
        }
    }

    handleConnectionFailure() {
        console.log('');
        console.log('⚠️  無法連接到 FUTU OpenD 可視化模式');
        console.log('💡 可能的原因:');
        console.log('   1. FUTU OpenD 未啟動');
        console.log('   2. API 服務未啟用');
        console.log('   3. WebSocket 服務未配置');
        console.log('   4. 防火牆阻擋連接');
        console.log('');
        console.log('🔧 建議解決方案:');
        console.log('   1. 確認 FUTU OpenD 已啟動並登錄');
        console.log('   2. 檢查 API 設置中的 WebSocket 配置');
        console.log('   3. 運行診斷工具: npm run diagnose');
        console.log('');
        console.log('📈 現在使用模擬數據模式演示功能...');

        this.isConnected = false;
        this.useRealData = false;
        this.connectionStatus = 'failed';
        this.broadcastStatus();
        this.startDataSimulation();
    }

    startRealDataMode() {
        console.log('📊 啟動真實數據模式');
        // 這裡可以添加真實數據處理邏輯
        // 例如訂閱期貨數據、處理推送等
    }

    handleRealData(data) {
        try {
            console.log('📨 處理真實數據');
            // 這裡添加真實數據解析和處理邏輯

            // 示例：解析並廣播數據
            const priceData = this.parseRealData(data);
            if (priceData) {
                this.broadcastPriceUpdate(priceData);
            }
        } catch (error) {
            console.error('❌ 處理真實數據失敗:', error);
        }
    }

    parseRealData(data) {
        // 這裡添加真實數據解析邏輯
        // 返回標準化的價格數據格式
        return null;
    }

    startDataSimulation() {
        if (this.useRealData || this.dataInterval) {
            return;
        }
        
        console.log('📈 啟動模擬數據模式...');
        console.log('ℹ️  這是演示數據，實際使用需要連接 FUTU OpenD');
        
        let basePrice = this.lastPrice || 19850;
        let volume = 12500;
        
        this.dataInterval = setInterval(() => {
            if (this.useRealData) {
                this.stopDataSimulation();
                return;
            }
            
            const change = (Math.random() - 0.5) * 20;
            basePrice += change;
            volume += Math.floor(Math.random() * 100);
            
            const priceData = {
                timestamp: new Date().toISOString(),
                price: parseFloat(basePrice.toFixed(2)),
                volume: volume,
                change: parseFloat(change.toFixed(2)),
                changePercent: parseFloat(((change / (basePrice - change)) * 100).toFixed(2)),
                isSimulated: true
            };

            this.lastPrice = priceData.price;
            this.priceHistory.push(priceData);

            if (this.priceHistory.length > 1000) {
                this.priceHistory.shift();
            }

            this.broadcastPriceUpdate(priceData);

        }, 1000);
    }

    stopDataSimulation() {
        if (this.dataInterval) {
            clearInterval(this.dataInterval);
            this.dataInterval = null;
        }
    }

    broadcastStatus() {
        const status = {
            connected: this.isConnected,
            contract: this.currentContract.code,
            contractName: this.currentContract.name,
            account: CONFIG.futuAccount,
            timestamp: new Date().toISOString(),
            dataType: this.useRealData ? 'real' : 'simulated',
            connectionStatus: this.connectionStatus,
            availableContracts: this.availableContracts.length
        };

        io.emit('status', status);
    }

    broadcastPriceUpdate(priceData) {
        io.emit('price_update', priceData);
    }

    cleanup() {
        this.stopDataSimulation();
        if (this.futuConnector) {
            this.futuConnector.disconnect();
            this.futuConnector = null;
        }
        server.close();
    }
}

// 創建並啟動應用
const tracker = new StandaloneFuturesTracker();

// 優雅關閉
process.on('SIGINT', () => {
    console.log('\n🛑 正在關閉程序...');
    tracker.cleanup();
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 正在關閉程序...');
    tracker.cleanup();
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

// 啟動應用
tracker.start().catch(error => {
    console.error('❌ 程序啟動失敗:', error);
    process.exit(1);
});

console.log('');
console.log('🎯 恆生指數期貨實時追蹤器 - 獨立版');
console.log('=====================================');
console.log('📝 使用說明:');
console.log('1. 程序會自動打開瀏覽器');
console.log('2. 如果 FUTU OpenD 運行，將顯示真實數據');
console.log('3. 否則顯示模擬數據供演示');
console.log('4. 按 Ctrl+C 退出程序');
console.log('');
console.log('💡 合約選擇:');
console.log(`   當前追蹤: ${HSI_FUTURES.code} (${HSI_FUTURES.name})`);
console.log('   要更改合約，請重新啟動程序並選擇');
console.log('=====================================');
console.log('');
