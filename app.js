#!/usr/bin/env node

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { spawn } = require('child_process');
const FutuVisualConnector = require('./futu-visual-connector');

// 配置信息
const CONFIG = {
    host: '127.0.0.1',
    port: 33333,
    enableSSL: false,
    websocketKey: '3e8229abd3ccdfdc',
    futuAccount: '********',
    password: '<PERSON>@0609'
};

// 小型恆生指數期貨合約選項
const MHI_CONTRACTS = {
    'MHI2501': { code: 'MHI2501', name: '2025年1月到期', market: 1 },
    'MHI2502': { code: 'MHI2502', name: '2025年2月到期', market: 1 },
    'MHI2503': { code: 'MHI2503', name: '2025年3月到期', market: 1 },
    'MHI2504': { code: 'MHI2504', name: '2025年4月到期', market: 1 },
    'MHI2505': { code: 'MHI2505', name: '2025年5月到期', market: 1 },
    'MHI2506': { code: 'MHI2506', name: '2025年6月到期', market: 1 }
};

// 解析命令行參數獲取合約
function getSelectedContract() {
    const args = process.argv.slice(2);
    let selectedContract = 'MHI2501'; // 預設合約

    // 查找 --contract 參數
    for (let i = 0; i < args.length; i++) {
        if (args[i].startsWith('--contract=')) {
            selectedContract = args[i].split('=')[1];
            break;
        }
    }

    // 驗證合約是否有效
    if (!MHI_CONTRACTS[selectedContract]) {
        console.log(`⚠️  無效的合約代碼: ${selectedContract}，使用預設合約 MHI2501`);
        selectedContract = 'MHI2501';
    }

    return MHI_CONTRACTS[selectedContract];
}

// 獲取選定的期貨合約
const HSI_FUTURES = getSelectedContract();

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 靜態文件服務
app.use(express.static(path.join(__dirname, 'public')));

// 主頁路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

class StandaloneFuturesTracker {
    constructor() {
        this.futuConnector = null;
        this.isConnected = false;
        this.lastPrice = null;
        this.priceHistory = [];
        this.clients = new Set();
        this.dataInterval = null;
        this.useRealData = false;
        this.serverPort = 3000;
        this.connectionStatus = 'disconnected';
    }

    async start() {
        console.log('🚀 啟動獨立版恆生指數期貨追蹤器...');
        console.log(`📊 目標合約: ${HSI_FUTURES.code} (${HSI_FUTURES.name})`);
        console.log(`🔑 使用帳號: ${CONFIG.futuAccount}`);
        console.log('');

        this.setupSocketIO();
        this.startWebServer();
        this.connectToFutu();
    }

    setupSocketIO() {
        io.on('connection', (socket) => {
            console.log('👤 新用戶連接:', socket.id);
            this.clients.add(socket);

            // 發送當前狀態
            socket.emit('status', {
                connected: this.isConnected,
                contract: HSI_FUTURES.code,
                contractName: HSI_FUTURES.name,
                account: CONFIG.futuAccount,
                dataType: this.useRealData ? 'real' : 'simulated'
            });

            // 發送歷史數據
            if (this.priceHistory.length > 0) {
                socket.emit('history', this.priceHistory.slice(-50));
            }

            socket.on('disconnect', () => {
                console.log('👤 用戶斷開:', socket.id);
                this.clients.delete(socket);
            });

            socket.on('reconnect_request', () => {
                this.connectToFutu();
            });
        });
    }

    startWebServer() {
        // 嘗試不同的端口
        const tryPorts = [3000, 3001, 3002, 3003, 8080, 8081];
        
        const tryNextPort = (portIndex = 0) => {
            if (portIndex >= tryPorts.length) {
                console.error('❌ 無法找到可用端口');
                process.exit(1);
                return;
            }

            const port = tryPorts[portIndex];
            
            server.listen(port, (err) => {
                if (err) {
                    console.log(`⚠️  端口 ${port} 被占用，嘗試下一個...`);
                    tryNextPort(portIndex + 1);
                } else {
                    this.serverPort = port;
                    console.log(`🌐 網頁服務器啟動在端口 ${port}`);
                    console.log(`📱 請在瀏覽器中訪問: http://localhost:${port}`);
                    
                    // 自動打開瀏覽器
                    this.openBrowser(`http://localhost:${port}`);
                }
            });

            server.on('error', (err) => {
                if (err.code === 'EADDRINUSE') {
                    console.log(`⚠️  端口 ${port} 被占用，嘗試下一個...`);
                    tryNextPort(portIndex + 1);
                }
            });
        };

        tryNextPort();
    }

    openBrowser(url) {
        const start = (process.platform == 'darwin'? 'open': process.platform == 'win32'? 'start': 'xdg-open');
        
        setTimeout(() => {
            console.log(`🔗 正在打開瀏覽器: ${url}`);
            spawn(start, [url], { shell: true, detached: true, stdio: 'ignore' });
        }, 2000);
    }

    async connectToFutu() {
        console.log(`🔗 正在嘗試連接到 FUTU OpenD 可視化模式...`);
        console.log(`📍 目標地址: ${CONFIG.host}:${CONFIG.port}`);
        console.log(`👤 使用帳號: ${CONFIG.futuAccount}`);

        this.connectionStatus = 'connecting';
        this.broadcastStatus();

        try {
            // 創建可視化模式連接器
            this.futuConnector = new FutuVisualConnector(CONFIG);

            // 設置事件監聽器
            this.futuConnector.onConnect = () => {
                console.log('✅ FUTU OpenD 可視化模式連接成功！');
                this.isConnected = true;
                this.useRealData = true;
                this.connectionStatus = 'connected';
                this.broadcastStatus();

                // 開始接收真實數據
                this.startRealDataMode();
            };

            this.futuConnector.onDisconnect = () => {
                console.log('❌ FUTU OpenD 連接斷開');
                this.isConnected = false;
                this.useRealData = false;
                this.connectionStatus = 'disconnected';
                this.broadcastStatus();
                this.startDataSimulation();
            };

            this.futuConnector.onError = (error) => {
                console.log('🚨 FUTU OpenD 連接錯誤:', error.message);
                this.handleConnectionFailure();
            };

            this.futuConnector.onData = (data) => {
                this.handleRealData(data);
            };

            // 嘗試連接
            const success = await this.futuConnector.connect();

            if (!success) {
                this.handleConnectionFailure();
            }

        } catch (error) {
            console.log('❌ 連接過程中發生錯誤:', error.message);
            this.handleConnectionFailure();
        }
    }

    handleConnectionFailure() {
        console.log('');
        console.log('⚠️  無法連接到 FUTU OpenD 可視化模式');
        console.log('💡 可能的原因:');
        console.log('   1. FUTU OpenD 未啟動');
        console.log('   2. API 服務未啟用');
        console.log('   3. WebSocket 服務未配置');
        console.log('   4. 防火牆阻擋連接');
        console.log('');
        console.log('🔧 建議解決方案:');
        console.log('   1. 確認 FUTU OpenD 已啟動並登錄');
        console.log('   2. 檢查 API 設置中的 WebSocket 配置');
        console.log('   3. 運行診斷工具: npm run diagnose');
        console.log('');
        console.log('📈 現在使用模擬數據模式演示功能...');

        this.isConnected = false;
        this.useRealData = false;
        this.connectionStatus = 'failed';
        this.broadcastStatus();
        this.startDataSimulation();
    }

    startRealDataMode() {
        console.log('📊 啟動真實數據模式');
        // 這裡可以添加真實數據處理邏輯
        // 例如訂閱期貨數據、處理推送等
    }

    handleRealData(data) {
        try {
            console.log('📨 處理真實數據');
            // 這裡添加真實數據解析和處理邏輯

            // 示例：解析並廣播數據
            const priceData = this.parseRealData(data);
            if (priceData) {
                this.broadcastPriceUpdate(priceData);
            }
        } catch (error) {
            console.error('❌ 處理真實數據失敗:', error);
        }
    }

    parseRealData(data) {
        // 這裡添加真實數據解析邏輯
        // 返回標準化的價格數據格式
        return null;
    }

    startDataSimulation() {
        if (this.useRealData || this.dataInterval) {
            return;
        }
        
        console.log('📈 啟動模擬數據模式...');
        console.log('ℹ️  這是演示數據，實際使用需要連接 FUTU OpenD');
        
        let basePrice = this.lastPrice || 19850;
        let volume = 12500;
        
        this.dataInterval = setInterval(() => {
            if (this.useRealData) {
                this.stopDataSimulation();
                return;
            }
            
            const change = (Math.random() - 0.5) * 20;
            basePrice += change;
            volume += Math.floor(Math.random() * 100);
            
            const priceData = {
                timestamp: new Date().toISOString(),
                price: parseFloat(basePrice.toFixed(2)),
                volume: volume,
                change: parseFloat(change.toFixed(2)),
                changePercent: parseFloat(((change / (basePrice - change)) * 100).toFixed(2)),
                isSimulated: true
            };

            this.lastPrice = priceData.price;
            this.priceHistory.push(priceData);

            if (this.priceHistory.length > 1000) {
                this.priceHistory.shift();
            }

            this.broadcastPriceUpdate(priceData);

        }, 1000);
    }

    stopDataSimulation() {
        if (this.dataInterval) {
            clearInterval(this.dataInterval);
            this.dataInterval = null;
        }
    }

    broadcastStatus() {
        const status = {
            connected: this.isConnected,
            contract: HSI_FUTURES.code,
            contractName: HSI_FUTURES.name,
            account: CONFIG.futuAccount,
            timestamp: new Date().toISOString(),
            dataType: this.useRealData ? 'real' : 'simulated',
            connectionStatus: this.connectionStatus
        };

        io.emit('status', status);
    }

    broadcastPriceUpdate(priceData) {
        io.emit('price_update', priceData);
    }

    cleanup() {
        this.stopDataSimulation();
        if (this.futuConnector) {
            this.futuConnector.disconnect();
            this.futuConnector = null;
        }
        server.close();
    }
}

// 創建並啟動應用
const tracker = new StandaloneFuturesTracker();

// 優雅關閉
process.on('SIGINT', () => {
    console.log('\n🛑 正在關閉程序...');
    tracker.cleanup();
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 正在關閉程序...');
    tracker.cleanup();
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

// 啟動應用
tracker.start().catch(error => {
    console.error('❌ 程序啟動失敗:', error);
    process.exit(1);
});

console.log('');
console.log('🎯 恆生指數期貨實時追蹤器 - 獨立版');
console.log('=====================================');
console.log('📝 使用說明:');
console.log('1. 程序會自動打開瀏覽器');
console.log('2. 如果 FUTU OpenD 運行，將顯示真實數據');
console.log('3. 否則顯示模擬數據供演示');
console.log('4. 按 Ctrl+C 退出程序');
console.log('');
console.log('💡 合約選擇:');
console.log(`   當前追蹤: ${HSI_FUTURES.code} (${HSI_FUTURES.name})`);
console.log('   要更改合約，請重新啟動程序並選擇');
console.log('=====================================');
console.log('');
