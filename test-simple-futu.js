const WebSocket = require('ws');

// 簡單的 FUTU WebSocket 測試
class SimpleFutuTest {
    constructor() {
        this.ws = null;
        this.isConnected = false;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                console.log('🔗 連接到 FUTU OpenD WebSocket...');
                this.ws = new WebSocket('ws://127.0.0.1:33333');

                this.ws.on('open', () => {
                    console.log('✅ WebSocket 連接建立');
                    this.isConnected = true;
                    resolve(true);
                });

                this.ws.on('message', (data) => {
                    console.log('📨 收到消息:', data.toString());
                    try {
                        const response = JSON.parse(data.toString());
                        console.log('📋 解析後的響應:', JSON.stringify(response, null, 2));
                    } catch (error) {
                        console.log('⚠️  無法解析 JSON:', error.message);
                    }
                });

                this.ws.on('close', () => {
                    console.log('❌ WebSocket 連接關閉');
                    this.isConnected = false;
                });

                this.ws.on('error', (error) => {
                    console.log('❌ WebSocket 錯誤:', error.message);
                    reject(error);
                });

                // 5秒超時
                setTimeout(() => {
                    if (!this.isConnected) {
                        reject(new Error('連接超時'));
                    }
                }, 5000);

            } catch (error) {
                reject(error);
            }
        });
    }

    // 測試不同的請求格式
    async testDifferentFormats() {
        if (!this.isConnected) {
            console.log('❌ 未連接到 FUTU OpenD');
            return;
        }

        console.log('🧪 測試不同的請求格式...');

        // 格式1: 基本 JSON
        console.log('📤 測試格式1: 基本 JSON');
        const request1 = {
            cmd: 'GetFutureInfo',
            data: {
                securityList: [
                    { market: 1, code: 'MHImain' },
                    { market: 1, code: 'HSImain' }
                ]
            }
        };
        this.ws.send(JSON.stringify(request1));
        await this.wait(2000);

        // 格式2: 協議格式
        console.log('📤 測試格式2: 協議格式');
        const request2 = {
            Protocol: 'Qot_GetFutureInfo',
            Version: 1,
            ReqParam: {
                c2s: {
                    securityList: [
                        { market: 1, code: 'MHImain' },
                        { market: 1, code: 'HSImain' }
                    ]
                }
            }
        };
        this.ws.send(JSON.stringify(request2));
        await this.wait(2000);

        // 格式3: 簡化格式
        console.log('📤 測試格式3: 簡化格式');
        const request3 = {
            c2s: {
                securityList: [
                    { market: 1, code: 'MHImain' }
                ]
            }
        };
        this.ws.send(JSON.stringify(request3));
        await this.wait(2000);

        // 格式4: 查詢可用合約
        console.log('📤 測試格式4: 查詢市場合約');
        const request4 = {
            cmd: 'GetMarketState',
            data: {
                market: 1
            }
        };
        this.ws.send(JSON.stringify(request4));
        await this.wait(2000);

        // 格式5: 訂閱行情
        console.log('📤 測試格式5: 訂閱行情');
        const request5 = {
            cmd: 'Sub',
            data: {
                securityList: [
                    { market: 1, code: 'HSImain' }
                ],
                subTypeList: [1] // 基本報價
            }
        };
        this.ws.send(JSON.stringify(request5));
        await this.wait(2000);
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 主測試函數
async function runSimpleTest() {
    console.log('🚀 開始簡單 FUTU WebSocket 測試');
    console.log('================================');

    const test = new SimpleFutuTest();

    try {
        await test.connect();
        console.log('');
        
        await test.testDifferentFormats();
        
        console.log('');
        console.log('⏳ 等待響應...');
        await test.wait(5000);
        
        console.log('✅ 測試完成');
        
    } catch (error) {
        console.error('❌ 測試失敗:', error.message);
    } finally {
        test.disconnect();
    }
}

// 如果直接運行此文件
if (require.main === module) {
    runSimpleTest().catch(error => {
        console.error('❌ 測試過程中發生錯誤:', error);
        process.exit(1);
    });
}

module.exports = SimpleFutuTest;
